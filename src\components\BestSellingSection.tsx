import React from 'react';
import { Link } from 'react-router-dom';
import { Star, ShoppingCart, Heart, Loader2, Check } from 'lucide-react';
import { motion } from 'framer-motion';
import { useProducts } from '../hooks/useApi';
import { useCart } from '../context/CartContext';

const BestSellingSection = () => {
  const { data: products, loading, error } = useProducts({
    filters: { sortBy: 'rating', sortOrder: 'desc' },
    limit: 6
  });
  const { addToCart } = useCart();

  const [wishlist, setWishlist] = React.useState<number[]>([]);

  const toggleWishlist = (productId: number) => {
    setWishlist(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  if (loading) {
    return (
      <section className="py-20" style={{ backgroundColor: '#2a2a2a' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin" style={{ color: '#ef4444' }} />
            <span className="ml-2" style={{ color: '#f8fafc' }}>Loading products...</span>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-20" style={{ backgroundColor: '#2a2a2a' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center" style={{ color: '#ef4444' }}>
            Error loading products: {error}
          </div>
        </div>
      </section>
    );
  }

  const staticProducts = [
    {
      id: 1,
      name: "DANFU Pro Running Shoes",
      price: 149.99,
      originalPrice: 199.99,
      rating: 4.8,
      reviews: 324,
      image: "https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg?auto=compress&cs=tinysrgb&w=400",
      badge: "Best Seller"
    },
    {
      id: 2,
      name: "Elite Performance T-Shirt",
      price: 39.99,
      rating: 4.9,
      reviews: 156,
      image: "https://images.pexels.com/photos/8532616/pexels-photo-8532616.jpeg?auto=compress&cs=tinysrgb&w=400",
      badge: "New"
    },
    {
      id: 3,
      name: "Professional Soccer Cleats",
      price: 199.99,
      rating: 4.7,
      reviews: 89,
      image: "https://images.pexels.com/photos/2526878/pexels-photo-2526878.jpeg?auto=compress&cs=tinysrgb&w=400",
      badge: "Popular"
    },
    {
      id: 4,
      name: "Training Resistance Bands Set",
      price: 29.99,
      originalPrice: 49.99,
      rating: 4.6,
      reviews: 203,
      image: "https://images.pexels.com/photos/4162449/pexels-photo-4162449.jpeg?auto=compress&cs=tinysrgb&w=400",
      badge: "Sale"
    },
    {
      id: 5,
      name: "Premium Yoga Mat",
      price: 79.99,
      rating: 4.8,
      reviews: 127,
      image: "https://images.pexels.com/photos/4056723/pexels-photo-4056723.jpeg?auto=compress&cs=tinysrgb&w=400",
      badge: "Eco-Friendly"
    },
    {
      id: 6,
      name: "Wireless Sports Earbuds",
      price: 89.99,
      originalPrice: 129.99,
      rating: 4.5,
      reviews: 78,
      image: "https://images.pexels.com/photos/3780681/pexels-photo-3780681.jpeg?auto=compress&cs=tinysrgb&w=400",
      badge: "Limited"
    }
  ];

  return (
    <section className="py-20" style={{ backgroundColor: 'rgb(26, 26, 26)' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4" style={{ color: '#f8fafc' }}>Top Performers</h2>
          <p className="text-xl max-w-3xl mx-auto" style={{ color: '#94a3b8' }}>
            Discover our most popular products trusted by athletes worldwide for their 
            exceptional quality and performance.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {(products || staticProducts).map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ y: -8, scale: 1.02 }}
              className="border rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 group"
              style={{
                backgroundColor: 'rgb(42, 42, 42)',
                borderColor: '#4a4a4a',
                '--tw-shadow-color': '#ef4444'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = '#ef4444';
                e.currentTarget.style.boxShadow = '0 25px 50px -12px rgba(239, 68, 68, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = '#4a4a4a';
                e.currentTarget.style.boxShadow = '';
              }}
            >
              <Link to={`/product/${product.id}`} className="block">
              <div className="relative overflow-hidden">
                <motion.img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-48 object-cover"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.4 }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                <motion.div
                  className="absolute top-3 left-3"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <span className="text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg" style={{ backgroundColor: '#ef4444' }}>
                    {product.badge}
                  </span>
                </motion.div>

                <motion.button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    toggleWishlist(product.id);
                  }}
                  className="absolute top-3 right-3 p-2 backdrop-blur-sm rounded-full text-white hover:scale-110 transition-all duration-200"
                  style={{
                    backgroundColor: wishlist.includes(product.id) ? '#ef4444' : 'rgba(42, 42, 42, 0.8)'
                  }}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Heart
                    className="h-4 w-4"
                    fill={wishlist.includes(product.id) ? 'currentColor' : 'none'}
                  />
                </motion.button>

                {/* Quick view button - appears on hover */}
                <motion.div
                  className="absolute inset-x-4 bottom-4 opacity-0 group-hover:opacity-100 transition-all duration-300"
                  initial={{ y: 20 }}
                  whileHover={{ y: 0 }}
                >
                  <button className="w-full bg-white/90 backdrop-blur-sm text-black py-2 rounded-lg font-medium hover:bg-white transition-colors">
                    Quick View
                  </button>
                </motion.div>
              </div>
              
              <div className="p-4">
                <motion.h3
                  className="text-base font-semibold mb-2 transition-colors line-clamp-2"
                  style={{ color: '#f8fafc' }}
                  whileHover={{ scale: 1.02 }}
                  onMouseEnter={(e) => e.target.style.color = '#ef4444'}
                  onMouseLeave={(e) => e.target.style.color = '#f8fafc'}
                >
                  {product.name}
                </motion.h3>

                <div className="flex items-center mb-3">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <motion.div
                        key={i}
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.3 + i * 0.1 }}
                      >
                        <Star
                          className={`h-3 w-3 ${i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-600'}`}
                        />
                      </motion.div>
                    ))}
                  </div>
                  <span className="text-xs ml-2" style={{ color: '#94a3b8' }}>
                    {product.rating} ({product.reviews})
                  </span>
                </div>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <motion.span
                      className="text-xl font-bold"
                      style={{ color: '#f8fafc' }}
                      whileHover={{ scale: 1.05 }}
                    >
                      ${product.price}
                    </motion.span>
                    {product.originalPrice && (
                      <span className="text-sm line-through" style={{ color: '#94a3b8' }}>${product.originalPrice}</span>
                    )}
                  </div>
                  {product.originalPrice && (
                    <span className="text-xs text-white px-2 py-1 rounded-full" style={{ backgroundColor: '#22c55e' }}>
                      Save ${(product.originalPrice - product.price).toFixed(2)}
                    </span>
                  )}
                </div>

                <div className="flex space-x-2">
                  <motion.button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      addToCart(product);
                    }}
                    className="flex-1 text-white py-2.5 rounded-lg font-medium flex items-center justify-center gap-2 transition-colors"
                    style={{ backgroundColor: '#ef4444' }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = '#dc2626'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#ef4444'}
                  >
                    <ShoppingCart className="h-4 w-4" />
                    Add to Cart
                  </motion.button>
                  <Link to={`/product/${product.id}`}>
                    <motion.button
                      className="px-3 py-2.5 rounded-lg transition-colors"
                      style={{ border: '1px solid #4a4a4a', color: '#f8fafc' }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onMouseEnter={(e) => e.target.style.borderColor = '#ef4444'}
                      onMouseLeave={(e) => e.target.style.borderColor = '#4a4a4a'}
                    >
                      View
                    </motion.button>
                  </Link>
                </div>
              </div>
              </Link>
            </motion.div>
          ))}
        </div>

        <div className="text-center mt-12">
          <button className="bg-transparent border-2 border-red-500 text-red-500 hover:bg-red-500 hover:text-white px-8 py-3 rounded-lg font-semibold transition-colors">
            View All Products
          </button>
        </div>
      </div>
    </section>
  );
};

export default BestSellingSection;