const express = require('express');
const {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  addReview,
  getProductReviews,
  getSellerProducts
} = require('../controllers/productController');
const { validate, validateQuery, querySchemas } = require('../middleware/validation');
const { verifyToken, requireSeller, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.get('/', validateQuery(querySchemas.productFilters), optionalAuth, getProducts);
router.get('/:id', optionalAuth, getProduct);
router.get('/:id/reviews', getProductReviews);

// Protected routes
router.use(verifyToken);

// Review routes
router.post('/:id/reviews', validate('createReview'), addReview);

// Seller/Admin routes
router.post('/', requireSeller, validate('createProduct'), createProduct);
router.put('/:id', requireSeller, validate('createProduct'), updateProduct);
router.delete('/:id', requireSeller, deleteProduct);

// Seller specific routes
router.get('/seller/:sellerId', getSellerProducts);
router.get('/my/products', getSellerProducts);

module.exports = router;
