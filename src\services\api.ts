import axios, { AxiosResponse } from 'axios';
import { io, Socket } from 'socket.io-client';
import {
  User,
  Product,
  Category,
  LoginData,
  SignupData,
  AuthResponse,
  ApiResponse,
  ProductFilters,
  CartItem,
  CartState
} from '../types';
import {
  transformProduct,
  transformUser,
  transformCategory,
  transformCart,
  transformPaginatedResponse,
  transformError
} from '../utils/dataTransform';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
const SOCKET_URL = import.meta.env.VITE_SOCKET_URL || 'http://localhost:5000';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Socket.io instance
let socket: Socket | null = null;

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('danfu_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('danfu_refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, {
            refreshToken
          });

          const { token, refreshToken: newRefreshToken } = response.data;
          localStorage.setItem('danfu_token', token);
          localStorage.setItem('danfu_refresh_token', newRefreshToken);

          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('danfu_token');
        localStorage.removeItem('danfu_refresh_token');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// Socket.io functions
export const initializeSocket = (userId?: string) => {
  if (!socket) {
    socket = io(SOCKET_URL, {
      autoConnect: false,
    });

    socket.on('connect', () => {
      console.log('Connected to server');
      if (userId) {
        socket?.emit('join-user-room', userId);
      }
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from server');
    });
  }

  if (!socket.connected) {
    socket.connect();
  }

  return socket;
};

export const disconnectSocket = () => {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
};

export const getSocket = () => socket;

// Authentication API
export const authApi = {
  login: async (data: LoginData): Promise<AuthResponse> => {
    const response: AxiosResponse<AuthResponse> = await apiClient.post('/auth/login', data);
    
    // Store tokens
    localStorage.setItem('danfu_token', response.data.token);
    localStorage.setItem('danfu_refresh_token', response.data.refreshToken);
    
    // Initialize socket connection
    initializeSocket(response.data.user.id);
    
    return response.data;
  },

  signup: async (data: SignupData): Promise<AuthResponse> => {
    const response: AxiosResponse<AuthResponse> = await apiClient.post('/auth/register', data);
    
    // Store tokens
    localStorage.setItem('danfu_token', response.data.token);
    localStorage.setItem('danfu_refresh_token', response.data.refreshToken);
    
    // Initialize socket connection
    initializeSocket(response.data.user.id);
    
    return response.data;
  },

  logout: async (): Promise<void> => {
    try {
      await apiClient.post('/auth/logout');
    } finally {
      localStorage.removeItem('danfu_token');
      localStorage.removeItem('danfu_refresh_token');
      disconnectSocket();
    }
  },

  getMe: async (): Promise<User> => {
    const response: AxiosResponse<{ success: boolean; user?: any; data?: any }> = await apiClient.get('/auth/me');
    const userData = response.data.user || response.data.data;
    return transformUser(userData);
  },

  refreshToken: async (refreshToken: string): Promise<AuthResponse> => {
    const response: AxiosResponse<AuthResponse> = await apiClient.post('/auth/refresh-token', {
      refreshToken
    });
    
    localStorage.setItem('danfu_token', response.data.token);
    localStorage.setItem('danfu_refresh_token', response.data.refreshToken);
    
    return response.data;
  }
};

// Products API
export const productsApi = {
  getProducts: async (filters: ProductFilters = {}): Promise<{
    data: Product[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> => {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const response: AxiosResponse<{
      success: boolean;
      data: any[];
      pagination: any;
    }> = await apiClient.get(`/products?${params.toString()}`);

    return transformPaginatedResponse(response.data, transformProduct);
  },

  getProduct: async (id: number | string): Promise<Product> => {
    const response: AxiosResponse<{ success: boolean; data: any }> = await apiClient.get(`/products/${id}`);
    return transformProduct(response.data.data);
  },

  createProduct: async (data: Partial<Product>): Promise<Product> => {
    const response: AxiosResponse<ApiResponse<Product>> = await apiClient.post('/products', data);
    return response.data.data;
  },

  updateProduct: async (id: number | string, data: Partial<Product>): Promise<Product> => {
    const response: AxiosResponse<ApiResponse<Product>> = await apiClient.put(`/products/${id}`, data);
    return response.data.data;
  },

  deleteProduct: async (id: number | string): Promise<void> => {
    await apiClient.delete(`/products/${id}`);
  },

  addReview: async (productId: number | string, review: { rating: number; comment: string }): Promise<any> => {
    const response: AxiosResponse<ApiResponse<any>> = await apiClient.post(`/products/${productId}/reviews`, review);
    return response.data.data;
  }
};

// Cart API
export const cartApi = {
  getCart: async (): Promise<CartState> => {
    const response: AxiosResponse<{ success: boolean; data: any }> = await apiClient.get('/cart');
    return transformCart(response.data.data);
  },

  addToCart: async (data: {
    productId: string;
    quantity: number;
    selectedSize?: string;
    selectedColor?: string;
  }): Promise<CartState> => {
    const response: AxiosResponse<ApiResponse<CartState>> = await apiClient.post('/cart/add', data);

    // Transform backend cart data to frontend format
    const cartData = response.data.data;
    const transformedCart = {
      ...cartData,
      items: cartData.items.map((item: any) => ({
        id: item._id || item.id,
        productId: typeof item.product === 'object' ? item.product._id || item.product.id : item.product,
        product: typeof item.product === 'object' ? {
          ...item.product,
          id: item.product._id || item.product.id,
          rating: item.product.averageRating || item.product.rating || 0,
          reviews: item.product.reviewCount || item.product.reviews || 0,
          image: item.product.images?.[0] || item.product.image || '',
          inventory: { quantity: item.product.stock || 0 }
        } : item.product,
        quantity: item.quantity,
        selectedSize: item.selectedSize,
        selectedColor: item.selectedColor,
        addedAt: item.addedAt
      }))
    };

    // Emit socket event for real-time updates
    const socket = getSocket();
    if (socket) {
      socket.emit('cart-updated', {
        userId: getCurrentUserId(),
        cart: transformedCart
      });
    }

    return transformedCart;
  },

  updateCartItem: async (itemId: string, quantity: number): Promise<CartState> => {
    const response: AxiosResponse<ApiResponse<CartState>> = await apiClient.put(`/cart/items/${itemId}`, {
      quantity
    });
    
    // Emit socket event for real-time updates
    const socket = getSocket();
    if (socket) {
      socket.emit('cart-updated', {
        userId: getCurrentUserId(),
        cart: response.data.data
      });
    }
    
    return response.data.data;
  },

  removeFromCart: async (itemId: string): Promise<CartState> => {
    const response: AxiosResponse<ApiResponse<CartState>> = await apiClient.delete(`/cart/items/${itemId}`);
    
    // Emit socket event for real-time updates
    const socket = getSocket();
    if (socket) {
      socket.emit('cart-updated', {
        userId: getCurrentUserId(),
        cart: response.data.data
      });
    }
    
    return response.data.data;
  },

  clearCart: async (): Promise<CartState> => {
    const response: AxiosResponse<ApiResponse<CartState>> = await apiClient.delete('/cart/clear');
    
    // Emit socket event for real-time updates
    const socket = getSocket();
    if (socket) {
      socket.emit('cart-updated', {
        userId: getCurrentUserId(),
        cart: response.data.data
      });
    }
    
    return response.data.data;
  },

  // Wishlist
  getWishlist: async (): Promise<Product[]> => {
    const response: AxiosResponse<ApiResponse<Product[]>> = await apiClient.get('/cart/wishlist');
    return response.data.data;
  },

  addToWishlist: async (productId: string): Promise<void> => {
    await apiClient.post(`/cart/wishlist/${productId}`);
  },

  removeFromWishlist: async (productId: string): Promise<void> => {
    await apiClient.delete(`/cart/wishlist/${productId}`);
  }
};

// Categories API
export const categoriesApi = {
  getCategories: async (): Promise<Category[]> => {
    const response: AxiosResponse<{ success: boolean; data: any[] }> = await apiClient.get('/categories');
    return response.data.data.map(transformCategory);
  }
};

// Utility function to get current user ID
const getCurrentUserId = (): string | null => {
  try {
    const token = localStorage.getItem('danfu_token');
    if (!token) return null;
    
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.userId;
  } catch {
    return null;
  }
};

// Error handling utility
export const handleApiError = transformError;

export default {
  auth: authApi,
  products: productsApi,
  cart: cartApi,
  categories: categoriesApi
};
