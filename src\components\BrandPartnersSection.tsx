import React from 'react';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { useBrandPartners } from '../hooks/useApi';

const BrandPartnersSection = () => {
  const { data: partners, loading, error } = useBrandPartners();

  const staticPartners = [
    {
      name: "<PERSON>",
      logo: "https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png",
      description: "Global sportswear leader"
    },
    {
      name: "Adidas",
      logo: "https://logos-world.net/wp-content/uploads/2020/04/Adidas-Logo.png",
      description: "Performance innovation"
    },
    {
      name: "Under Armour",
      logo: "https://logos-world.net/wp-content/uploads/2020/04/Under-Armour-Logo.png",
      description: "Athletic excellence"
    },
    {
      name: "<PERSON><PERSON>",
      logo: "https://logos-world.net/wp-content/uploads/2020/04/Puma-Logo.png",
      description: "Forever faster"
    },
    {
      name: "New Balance",
      logo: "https://logos-world.net/wp-content/uploads/2020/04/New-Balance-Logo.png",
      description: "Endorsed by no one"
    },
    {
      name: "Reebok",
      logo: "https://logos-world.net/wp-content/uploads/2020/04/Reebok-Logo.png",
      description: "Be more human"
    },
    {
      name: "ASICS",
      logo: "https://logos-world.net/wp-content/uploads/2020/04/ASICS-Logo.png",
      description: "Sound mind, sound body"
    },
    {
      name: "Converse",
      logo: "https://logos-world.net/wp-content/uploads/2020/04/Converse-Logo.png",
      description: "All star heritage"
    }
  ];

  const currentPartners = partners || staticPartners;

  // Use static partners without duplication
  const displayPartners = currentPartners;

  if (loading) {
    return (
      <section className="bg-gray-900 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-red-500" />
            <span className="ml-2 text-white">Loading brand partners...</span>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="bg-gray-900 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-red-500">
            Error loading brand partners: {error}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 relative overflow-hidden" style={{ backgroundColor: '#2a2a2a' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-12">
        <div className="text-center">
          <motion.h2
            className="text-4xl font-bold mb-6"
            style={{ color: '#f8fafc' }}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            Trusted by Leading Brands
          </motion.h2>
          <motion.p
            className="text-xl max-w-3xl mx-auto"
            style={{ color: '#94a3b8' }}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            DANFU partners with the world's most respected athletic brands to bring you 
            the finest selection of premium sportswear and equipment.
          </motion.p>
        </div>
      </div>

      {/* Floating Animation Background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 rounded-full opacity-20"
            style={{
              backgroundColor: '#ef4444',
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
            animate={{
              y: [0, -100, 0],
              opacity: [0.2, 0.6, 0.2],
              scale: [1, 1.5, 1]
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Static partners grid */}
      <div className="relative">
        <div className="flex flex-wrap justify-center gap-8 max-w-6xl mx-auto">
          {displayPartners.map((partner, index) => (
            <motion.div
              key={`${partner.name}-${index}`}
              className="flex-shrink-0 w-56 h-36 rounded-2xl flex items-center justify-center p-8 transition-all duration-500 group cursor-pointer relative overflow-hidden"
              style={{
                backgroundColor: '#1a1a1a',
                border: '1px solid #4a4a4a'
              }}
              whileHover={{
                scale: 1.08,
                y: -8,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = '#ef4444';
                e.currentTarget.style.boxShadow = '0 20px 40px rgba(239, 68, 68, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = '#4a4a4a';
                e.currentTarget.style.boxShadow = '';
              }}
            >
              {/* Animated background gradient */}
              <motion.div
                className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-500"
                style={{
                  background: 'linear-gradient(45deg, #ef4444, transparent, #ef4444)'
                }}
                animate={{
                  backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "linear"
                }}
              />

              <div className="text-center relative z-10">
                <motion.div
                  className="h-20 flex items-center justify-center mb-3"
                  whileHover={{ rotate: [0, -5, 5, 0] }}
                  transition={{ duration: 0.5 }}
                >
                  <img
                    src={partner.logo}
                    alt={partner.name}
                    className="max-h-16 max-w-40 object-contain filter brightness-90 group-hover:brightness-110 transition-all duration-500"
                    style={{ filter: 'brightness(0.9) contrast(1.1)' }}
                    onMouseEnter={(e) => e.target.style.filter = 'brightness(1.1) contrast(1.2)'}
                    onMouseLeave={(e) => e.target.style.filter = 'brightness(0.9) contrast(1.1)'}
                  />
                </motion.div>
                <motion.p
                  className="text-xs font-medium transition-colors duration-300"
                  style={{ color: '#94a3b8' }}
                  whileHover={{ color: '#ef4444' }}
                >
                  {partner.description}
                </motion.p>
              </div>

              {/* Shine effect on hover */}
              <motion.div
                className="absolute inset-0 opacity-0 group-hover:opacity-100 pointer-events-none"
                style={{
                  background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)',
                  transform: 'translateX(-100%)'
                }}
                animate={{
                  transform: ['translateX(-100%)', 'translateX(100%)']
                }}
                transition={{
                  duration: 0.8,
                  ease: "easeInOut"
                }}
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Gradient overlays for smooth edges */}
        <div className="absolute top-0 left-0 w-32 h-full bg-gradient-to-r from-gray-900 to-transparent z-10 pointer-events-none"></div>
        <div className="absolute top-0 right-0 w-32 h-full bg-gradient-to-l from-gray-900 to-transparent z-10 pointer-events-none"></div>
      </div>

      {/* Stats section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-16">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {[
            { number: "50+", label: "Brand Partners", icon: "🤝" },
            { number: "1M+", label: "Products Available", icon: "📦" },
            { number: "99.9%", label: "Authentic Guarantee", icon: "✅" },
            { number: "24/7", label: "Partnership Support", icon: "🔄" }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-2xl mb-2">{stat.icon}</div>
              <div className="text-2xl font-bold text-white mb-1">{stat.number}</div>
              <div className="text-sm text-gray-400">{stat.label}</div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BrandPartnersSection;
