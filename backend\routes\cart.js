const express = require('express');
const {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  addToWishlist,
  removeFromWishlist,
  getWishlist
} = require('../controllers/cartController');
const { validate } = require('../middleware/validation');
const { verifyToken } = require('../middleware/auth');

const router = express.Router();

// All cart routes require authentication
router.use(verifyToken);

// Cart routes
router.get('/', getCart);
router.post('/add', validate('addToCart'), addToCart);
router.put('/items/:itemId', validate('updateCartItem'), updateCartItem);
router.delete('/items/:itemId', removeFromCart);
router.delete('/clear', clearCart);

// Wishlist routes
router.get('/wishlist', getWishlist);
router.post('/wishlist/:productId', addToWishlist);
router.delete('/wishlist/:productId', removeFromWishlist);

module.exports = router;
