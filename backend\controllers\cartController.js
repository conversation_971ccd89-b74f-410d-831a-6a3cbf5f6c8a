const User = require('../models/User');
const Product = require('../models/Product');
const { AppError, catchAsync } = require('../middleware/errorHandler');

// Get user's cart
const getCart = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user._id)
    .populate({
      path: 'cart.product',
      select: 'name price images stock status averageRating'
    });

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Filter out products that no longer exist or are inactive
  const validCartItems = user.cart.filter(item => 
    item.product && item.product.status === 'active'
  );

  // Update cart if items were removed
  if (validCartItems.length !== user.cart.length) {
    user.cart = validCartItems;
    await user.save();
  }

  // Calculate totals
  const cartSummary = {
    items: user.cart,
    totalItems: user.cart.reduce((sum, item) => sum + item.quantity, 0),
    totalAmount: user.cart.reduce((sum, item) => {
      const price = item.product.price || 0;
      return sum + (price * item.quantity);
    }, 0)
  };

  res.status(200).json({
    success: true,
    data: cartSummary
  });
});

// Add item to cart
const addToCart = catchAsync(async (req, res, next) => {
  const { productId, quantity, selectedSize, selectedColor } = req.validatedData;

  // Check if product exists and is available
  const product = await Product.findById(productId);
  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  if (product.status !== 'active') {
    return next(new AppError('Product is not available', 400));
  }

  if (product.stock < quantity) {
    return next(new AppError('Insufficient stock available', 400));
  }

  const user = await User.findById(req.user._id);

  // Check if item already exists in cart
  const existingItemIndex = user.cart.findIndex(item => 
    item.product.toString() === productId &&
    item.selectedSize === selectedSize &&
    item.selectedColor === selectedColor
  );

  if (existingItemIndex >= 0) {
    // Update quantity of existing item
    const newQuantity = user.cart[existingItemIndex].quantity + quantity;
    
    if (newQuantity > product.stock) {
      return next(new AppError('Cannot add more items than available stock', 400));
    }
    
    user.cart[existingItemIndex].quantity = newQuantity;
    user.cart[existingItemIndex].addedAt = new Date();
  } else {
    // Add new item to cart
    user.cart.push({
      product: productId,
      quantity,
      selectedSize,
      selectedColor,
      addedAt: new Date()
    });
  }

  await user.save();

  // Populate the cart for response
  await user.populate({
    path: 'cart.product',
    select: 'name price images stock status averageRating'
  });

  // Calculate totals
  const cartSummary = {
    items: user.cart,
    totalItems: user.cart.reduce((sum, item) => sum + item.quantity, 0),
    totalAmount: user.cart.reduce((sum, item) => {
      const price = item.product.price || 0;
      return sum + (price * item.quantity);
    }, 0)
  };

  res.status(200).json({
    success: true,
    message: 'Item added to cart successfully',
    data: cartSummary
  });
});

// Update cart item quantity
const updateCartItem = catchAsync(async (req, res, next) => {
  const { itemId } = req.params;
  const { quantity } = req.validatedData;

  const user = await User.findById(req.user._id);
  const cartItem = user.cart.id(itemId);

  if (!cartItem) {
    return next(new AppError('Cart item not found', 404));
  }

  // Check product stock
  const product = await Product.findById(cartItem.product);
  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  if (quantity > product.stock) {
    return next(new AppError('Insufficient stock available', 400));
  }

  cartItem.quantity = quantity;
  await user.save();

  // Populate the cart for response
  await user.populate({
    path: 'cart.product',
    select: 'name price images stock status averageRating'
  });

  // Calculate totals
  const cartSummary = {
    items: user.cart,
    totalItems: user.cart.reduce((sum, item) => sum + item.quantity, 0),
    totalAmount: user.cart.reduce((sum, item) => {
      const price = item.product.price || 0;
      return sum + (price * item.quantity);
    }, 0)
  };

  res.status(200).json({
    success: true,
    message: 'Cart item updated successfully',
    data: cartSummary
  });
});

// Remove item from cart
const removeFromCart = catchAsync(async (req, res, next) => {
  const { itemId } = req.params;

  const user = await User.findById(req.user._id);
  const cartItem = user.cart.id(itemId);

  if (!cartItem) {
    return next(new AppError('Cart item not found', 404));
  }

  cartItem.remove();
  await user.save();

  // Populate the cart for response
  await user.populate({
    path: 'cart.product',
    select: 'name price images stock status averageRating'
  });

  // Calculate totals
  const cartSummary = {
    items: user.cart,
    totalItems: user.cart.reduce((sum, item) => sum + item.quantity, 0),
    totalAmount: user.cart.reduce((sum, item) => {
      const price = item.product.price || 0;
      return sum + (price * item.quantity);
    }, 0)
  };

  res.status(200).json({
    success: true,
    message: 'Item removed from cart successfully',
    data: cartSummary
  });
});

// Clear entire cart
const clearCart = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user._id);
  user.cart = [];
  await user.save();

  res.status(200).json({
    success: true,
    message: 'Cart cleared successfully',
    data: {
      items: [],
      totalItems: 0,
      totalAmount: 0
    }
  });
});

// Add item to wishlist
const addToWishlist = catchAsync(async (req, res, next) => {
  const { productId } = req.params;

  // Check if product exists
  const product = await Product.findById(productId);
  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  const user = await User.findById(req.user._id);

  // Check if already in wishlist
  if (user.wishlist.includes(productId)) {
    return next(new AppError('Product already in wishlist', 400));
  }

  user.wishlist.push(productId);
  await user.save();

  res.status(200).json({
    success: true,
    message: 'Product added to wishlist successfully'
  });
});

// Remove item from wishlist
const removeFromWishlist = catchAsync(async (req, res, next) => {
  const { productId } = req.params;

  const user = await User.findById(req.user._id);
  user.wishlist = user.wishlist.filter(id => id.toString() !== productId);
  await user.save();

  res.status(200).json({
    success: true,
    message: 'Product removed from wishlist successfully'
  });
});

// Get user's wishlist
const getWishlist = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.user._id)
    .populate({
      path: 'wishlist',
      select: 'name price images averageRating stock status'
    });

  res.status(200).json({
    success: true,
    data: user.wishlist
  });
});

module.exports = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  addToWishlist,
  removeFromWishlist,
  getWishlist
};
