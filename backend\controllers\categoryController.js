const Category = require('../models/Category');
const { AppError, catchAsync } = require('../middleware/errorHandler');

// Get all categories
const getCategories = catchAsync(async (req, res, next) => {
  const { includeInactive = false } = req.query;
  
  const filter = includeInactive === 'true' ? {} : { isActive: true };
  
  const categories = await Category.find(filter)
    .populate('parent', 'name slug')
    .populate('children', 'name slug')
    .sort({ level: 1, sortOrder: 1 });

  res.status(200).json({
    success: true,
    data: categories
  });
});

// Get category tree
const getCategoryTree = catchAsync(async (req, res, next) => {
  const tree = await Category.getTree();
  
  res.status(200).json({
    success: true,
    data: tree
  });
});

// Get single category
const getCategory = catchAsync(async (req, res, next) => {
  const category = await Category.findById(req.params.id)
    .populate('parent', 'name slug')
    .populate('children', 'name slug');

  if (!category) {
    return next(new AppError('Category not found', 404));
  }

  res.status(200).json({
    success: true,
    data: category
  });
});

// Create category (admin only)
const createCategory = catchAsync(async (req, res, next) => {
  const categoryData = req.validatedData;

  // If parent is specified, verify it exists and set level
  if (categoryData.parent) {
    const parentCategory = await Category.findById(categoryData.parent);
    if (!parentCategory) {
      return next(new AppError('Parent category not found', 404));
    }
    categoryData.level = parentCategory.level + 1;
  } else {
    categoryData.level = 0;
  }

  const category = await Category.create(categoryData);

  // Update parent's children array
  if (category.parent) {
    await Category.findByIdAndUpdate(
      category.parent,
      { $push: { children: category._id } }
    );
  }

  await category.populate('parent', 'name slug');

  res.status(201).json({
    success: true,
    data: category
  });
});

// Update category (admin only)
const updateCategory = catchAsync(async (req, res, next) => {
  const category = await Category.findByIdAndUpdate(
    req.params.id,
    req.validatedData,
    { new: true, runValidators: true }
  )
    .populate('parent', 'name slug')
    .populate('children', 'name slug');

  if (!category) {
    return next(new AppError('Category not found', 404));
  }

  res.status(200).json({
    success: true,
    data: category
  });
});

// Delete category (admin only)
const deleteCategory = catchAsync(async (req, res, next) => {
  const category = await Category.findById(req.params.id);

  if (!category) {
    return next(new AppError('Category not found', 404));
  }

  // Check if category has children
  if (category.children && category.children.length > 0) {
    return next(new AppError('Cannot delete category with subcategories', 400));
  }

  // Check if category has products
  const Product = require('../models/Product');
  const productCount = await Product.countDocuments({ category: req.params.id });
  
  if (productCount > 0) {
    return next(new AppError('Cannot delete category with products', 400));
  }

  // Remove from parent's children array
  if (category.parent) {
    await Category.findByIdAndUpdate(
      category.parent,
      { $pull: { children: category._id } }
    );
  }

  await Category.findByIdAndDelete(req.params.id);

  res.status(204).json({
    success: true,
    data: null
  });
});

// Get featured categories
const getFeaturedCategories = catchAsync(async (req, res, next) => {
  const categories = await Category.find({ 
    isActive: true, 
    isFeatured: true 
  })
    .sort({ sortOrder: 1 })
    .limit(6);

  res.status(200).json({
    success: true,
    data: categories
  });
});

module.exports = {
  getCategories,
  getCategoryTree,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  getFeaturedCategories
};
