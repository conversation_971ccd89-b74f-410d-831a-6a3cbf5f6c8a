// Product related types
export interface Product {
  id: string;
  name: string;
  description?: string;
  shortDescription?: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  rating: number;
  reviews: number;
  averageRating?: number;
  reviewCount?: number;
  image: string;
  images?: string[];
  badge?: string;
  badges?: string[];
  category: string | Category;
  subcategory?: string;
  brand?: string;
  model?: string;
  sku?: string;
  slug?: string;
  stock: number;
  inventory?: {
    quantity: number;
    lowStockThreshold?: number;
  };
  specifications?: {
    size?: string[];
    color?: string[];
    material?: string;
    weight?: number;
    dimensions?: {
      length: number;
      width: number;
      height: number;
    };
  };
  sizes?: string[];
  colors?: string[];
  features?: string[];
  tags?: string[];
  isActive?: boolean;
  status?: 'active' | 'inactive' | 'out_of_stock' | 'discontinued';
  seller?: string | User;
  salesCount?: number;
  viewCount?: number;
  wishlistCount?: number;
  createdAt: string;
  updatedAt: string;
}

// Category related types
export interface Category {
  id: string;
  name: string;
  title?: string;
  description: string;
  slug: string;
  itemCount?: string;
  productCount?: number;
  image?: string;
  icon?: string;
  category?: 'sportswear' | 'sportsgear';
  parent?: string | Category;
  parentCategory?: string;
  children?: Category[];
  level?: number;
  isActive: boolean;
  isFeatured?: boolean;
  sortOrder?: number;
  metaTitle?: string;
  metaDescription?: string;
  createdAt: string;
  updatedAt?: string;
}

// Review related types
export interface Review {
  id: number;
  name: string;
  role: string;
  rating: number;
  comment: string;
  product: string;
  verified: boolean;
  avatar: string;
  userId?: number;
  productId?: number;
  createdAt: string;
}

// User related types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName?: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  avatar?: string;
  accountType: 'customer' | 'seller' | 'admin';
  sellerInfo?: {
    businessName: string;
    businessType: string;
    taxId?: string;
    businessAddress?: Address;
    isVerified: boolean;
    verificationDocuments?: string[];
    commissionRate: number;
    totalSales?: number;
    rating?: number;
    reviewCount?: number;
  };
  isEmailVerified: boolean;
  isActive?: boolean;
  addresses?: Address[];
  preferences?: {
    notifications: boolean;
    marketing: boolean;
    newsletter: boolean;
  };
  loyaltyPoints?: number;
  wishlist?: string[] | Product[];
  cart?: CartItem[];
  lastLogin?: string;
  loginCount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  id?: string;
  type?: 'billing' | 'shipping';
  firstName?: string;
  lastName?: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phoneNumber?: string;
  isDefault?: boolean;
}

// Order related types
export interface Order {
  id: number;
  userId: number;
  items: OrderItem[];
  totalAmount: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  shippingAddress: Address;
  billingAddress: Address;
  paymentMethod: string;
  tracking?: {
    carrier: string;
    trackingNumber: string;
    estimatedDelivery: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  productId: number;
  quantity: number;
  price: number;
  specifications?: {
    size?: string;
    color?: string;
  };
}

// Brand Partner types
export interface BrandPartner {
  id: number;
  name: string;
  logo: string;
  description: string;
  website?: string;
  isActive: boolean;
  sortOrder?: number;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message?: string;
}

// Search and Filter types
export interface ProductFilters {
  category?: string;
  subcategory?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  rating?: number;
  inStock?: boolean;
  sortBy?: 'price' | 'rating' | 'name' | 'newest';
  sortOrder?: 'asc' | 'desc';
}

export interface SearchParams {
  query?: string;
  filters?: ProductFilters;
  page?: number;
  limit?: number;
}

// Authentication types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface SignupData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  acceptTerms: boolean;
  subscribeNewsletter?: boolean;
  accountType?: 'customer' | 'seller';
  // Seller specific fields
  businessName?: string;
  businessType?: string;
  taxId?: string;
}

export interface AuthResponse {
  success: boolean;
  user: User;
  token: string;
  refreshToken: string;
  message?: string;
  error?: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

// Cart types
export interface CartItem {
  id?: string;
  _id?: string;
  productId?: string;
  product: Product | string;
  quantity: number;
  price?: number;
  selectedSize?: string;
  selectedColor?: string;
  seller?: string | User;
  addedAt: string;
}

export interface CartState {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  isOpen: boolean;
  loading: boolean;
}

export interface CartContextType {
  cart: CartState;
  addToCart: (product: Product, quantity?: number, options?: { size?: string; color?: string }) => void;
  removeFromCart: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  toggleCart: () => void;
}
