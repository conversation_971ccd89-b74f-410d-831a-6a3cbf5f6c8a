const Product = require('../models/Product');
const Category = require('../models/Category');
const { AppError, catchAsync } = require('../middleware/errorHandler');

// Get all products with filtering, sorting, and pagination
const getProducts = catchAsync(async (req, res, next) => {
  const {
    page = 1,
    limit = 10,
    sort = 'createdAt',
    order = 'desc',
    category,
    minPrice,
    maxPrice,
    brand,
    search,
    inStock,
    featured
  } = req.validatedQuery;

  // Build filter object
  const filter = { status: 'active' };

  if (category) {
    filter.category = category;
  }

  if (minPrice || maxPrice) {
    filter.price = {};
    if (minPrice) filter.price.$gte = minPrice;
    if (maxPrice) filter.price.$lte = maxPrice;
  }

  if (brand) {
    filter.brand = new RegExp(brand, 'i');
  }

  if (search) {
    filter.$text = { $search: search };
  }

  if (inStock) {
    filter.stock = { $gt: 0 };
  }

  if (featured) {
    filter.badges = 'featured';
  }

  // Build sort object
  const sortObj = {};
  sortObj[sort] = order === 'desc' ? -1 : 1;

  // Calculate pagination
  const skip = (page - 1) * limit;

  // Execute query
  const products = await Product.find(filter)
    .populate('category', 'name slug')
    .populate('seller', 'firstName lastName sellerInfo.businessName')
    .sort(sortObj)
    .skip(skip)
    .limit(parseInt(limit))
    .select('-reviews'); // Exclude reviews for list view

  // Get total count for pagination
  const total = await Product.countDocuments(filter);

  res.status(200).json({
    success: true,
    data: products,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit)
    }
  });
});

// Get single product by ID
const getProduct = catchAsync(async (req, res, next) => {
  const product = await Product.findById(req.params.id)
    .populate('category', 'name slug')
    .populate('seller', 'firstName lastName sellerInfo.businessName sellerInfo.rating')
    .populate('reviews.user', 'firstName lastName avatar');

  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  // Increment view count
  await product.incrementViewCount();

  res.status(200).json({
    success: true,
    data: product
  });
});

// Create new product (sellers and admins only)
const createProduct = catchAsync(async (req, res, next) => {
  const productData = { ...req.validatedData };

  // Set seller to current user (unless admin is creating for another seller)
  if (req.user.accountType !== 'admin') {
    productData.seller = req.user._id;
  }

  // Verify category exists
  const category = await Category.findById(productData.category);
  if (!category) {
    return next(new AppError('Category not found', 404));
  }

  const product = await Product.create(productData);

  // Populate the response
  await product.populate('category', 'name slug');
  await product.populate('seller', 'firstName lastName sellerInfo.businessName');

  res.status(201).json({
    success: true,
    data: product
  });
});

// Update product
const updateProduct = catchAsync(async (req, res, next) => {
  let product = await Product.findById(req.params.id);

  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  // Check ownership (sellers can only update their own products)
  if (req.user.accountType === 'seller' && product.seller.toString() !== req.user._id.toString()) {
    return next(new AppError('You can only update your own products', 403));
  }

  // Update product
  product = await Product.findByIdAndUpdate(
    req.params.id,
    req.validatedData,
    { new: true, runValidators: true }
  )
    .populate('category', 'name slug')
    .populate('seller', 'firstName lastName sellerInfo.businessName');

  res.status(200).json({
    success: true,
    data: product
  });
});

// Delete product
const deleteProduct = catchAsync(async (req, res, next) => {
  const product = await Product.findById(req.params.id);

  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  // Check ownership (sellers can only delete their own products)
  if (req.user.accountType === 'seller' && product.seller.toString() !== req.user._id.toString()) {
    return next(new AppError('You can only delete your own products', 403));
  }

  await Product.findByIdAndDelete(req.params.id);

  res.status(204).json({
    success: true,
    data: null
  });
});

// Add review to product
const addReview = catchAsync(async (req, res, next) => {
  const { rating, comment } = req.validatedData;
  const productId = req.params.id;

  const product = await Product.findById(productId);

  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  // Check if user already reviewed this product
  const existingReview = product.reviews.find(
    review => review.user.toString() === req.user._id.toString()
  );

  if (existingReview) {
    return next(new AppError('You have already reviewed this product', 400));
  }

  // Add review
  await product.addReview(req.user._id, rating, comment);

  // Get updated product with populated reviews
  const updatedProduct = await Product.findById(productId)
    .populate('reviews.user', 'firstName lastName avatar');

  res.status(201).json({
    success: true,
    data: updatedProduct.reviews[updatedProduct.reviews.length - 1]
  });
});

// Get product reviews
const getProductReviews = catchAsync(async (req, res, next) => {
  const { page = 1, limit = 10 } = req.query;

  const product = await Product.findById(req.params.id);

  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  const skip = (page - 1) * limit;
  const reviews = product.reviews
    .sort((a, b) => b.createdAt - a.createdAt)
    .slice(skip, skip + parseInt(limit));

  // Populate user data for reviews
  await Product.populate(reviews, {
    path: 'user',
    select: 'firstName lastName avatar'
  });

  res.status(200).json({
    success: true,
    data: reviews,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: product.reviews.length,
      pages: Math.ceil(product.reviews.length / limit)
    }
  });
});

// Get seller's products
const getSellerProducts = catchAsync(async (req, res, next) => {
  const { page = 1, limit = 10, status } = req.query;
  const sellerId = req.params.sellerId || req.user._id;

  // Check if user can access seller's products
  if (req.user.accountType === 'seller' && sellerId !== req.user._id.toString()) {
    return next(new AppError('You can only access your own products', 403));
  }

  const filter = { seller: sellerId };
  if (status) {
    filter.status = status;
  }

  const skip = (page - 1) * limit;

  const products = await Product.find(filter)
    .populate('category', 'name slug')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));

  const total = await Product.countDocuments(filter);

  res.status(200).json({
    success: true,
    data: products,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit)
    }
  });
});

module.exports = {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  addReview,
  getProductReviews,
  getSellerProducts
};
