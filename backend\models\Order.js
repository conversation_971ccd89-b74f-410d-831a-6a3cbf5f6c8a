const mongoose = require('mongoose');

const orderItemSchema = new mongoose.Schema({
  product: { type: mongoose.Schema.Types.ObjectId, ref: 'Product', required: true },
  quantity: { type: Number, required: true, min: 1 },
  price: { type: Number, required: true, min: 0 },
  selectedSize: String,
  selectedColor: String,
  seller: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }
});

const shippingAddressSchema = new mongoose.Schema({
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  street: { type: String, required: true },
  city: { type: String, required: true },
  state: { type: String, required: true },
  zipCode: { type: String, required: true },
  country: { type: String, required: true, default: 'USA' },
  phoneNumber: String
});

const orderSchema = new mongoose.Schema({
  orderNumber: { type: String, unique: true, required: true },
  
  // Customer information
  customer: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  
  // Order items
  items: [orderItemSchema],
  
  // Pricing
  subtotal: { type: Number, required: true, min: 0 },
  tax: { type: Number, required: true, min: 0 },
  shipping: { type: Number, required: true, min: 0 },
  discount: { type: Number, default: 0, min: 0 },
  total: { type: Number, required: true, min: 0 },
  
  // Addresses
  shippingAddress: shippingAddressSchema,
  billingAddress: shippingAddressSchema,
  
  // Status
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'],
    default: 'pending'
  },
  
  // Payment
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded', 'partially_refunded'],
    default: 'pending'
  },
  
  paymentMethod: {
    type: String,
    enum: ['credit_card', 'debit_card', 'paypal', 'stripe', 'cash_on_delivery'],
    required: true
  },
  
  paymentId: String,
  
  // Shipping
  shippingMethod: {
    type: String,
    enum: ['standard', 'express', 'overnight', 'pickup'],
    default: 'standard'
  },
  
  trackingNumber: String,
  estimatedDelivery: Date,
  actualDelivery: Date,
  
  // Timestamps for status changes
  statusHistory: [{
    status: String,
    timestamp: { type: Date, default: Date.now },
    note: String
  }],
  
  // Notes
  customerNotes: String,
  adminNotes: String,
  
  // Coupons and discounts
  couponCode: String,
  discountAmount: { type: Number, default: 0 },
  
  // Refund information
  refundAmount: { type: Number, default: 0 },
  refundReason: String,
  refundDate: Date
}, {
  timestamps: true
});

// Indexes
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ customer: 1, createdAt: -1 });
orderSchema.index({ status: 1 });
orderSchema.index({ paymentStatus: 1 });
orderSchema.index({ 'items.seller': 1 });

// Pre-save middleware to generate order number
orderSchema.pre('save', async function(next) {
  if (this.isNew) {
    const count = await this.constructor.countDocuments();
    this.orderNumber = `ORD-${Date.now()}-${(count + 1).toString().padStart(4, '0')}`;
  }
  next();
});

// Method to add status update
orderSchema.methods.updateStatus = function(newStatus, note = '') {
  this.status = newStatus;
  this.statusHistory.push({
    status: newStatus,
    timestamp: new Date(),
    note
  });
  
  return this.save();
};

// Method to calculate totals
orderSchema.methods.calculateTotals = function() {
  this.subtotal = this.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  this.total = this.subtotal + this.tax + this.shipping - this.discount;
  return this;
};

// Virtual for order age
orderSchema.virtual('orderAge').get(function() {
  return Math.floor((Date.now() - this.createdAt) / (1000 * 60 * 60 * 24));
});

// Static method to get sales statistics
orderSchema.statics.getSalesStats = async function(sellerId, startDate, endDate) {
  const pipeline = [
    {
      $match: {
        'items.seller': mongoose.Types.ObjectId(sellerId),
        status: { $in: ['delivered', 'shipped'] },
        createdAt: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $unwind: '$items'
    },
    {
      $match: {
        'items.seller': mongoose.Types.ObjectId(sellerId)
      }
    },
    {
      $group: {
        _id: null,
        totalSales: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
        totalOrders: { $sum: 1 },
        totalQuantity: { $sum: '$items.quantity' }
      }
    }
  ];
  
  const result = await this.aggregate(pipeline);
  return result[0] || { totalSales: 0, totalOrders: 0, totalQuantity: 0 };
};

module.exports = mongoose.model('Order', orderSchema);
