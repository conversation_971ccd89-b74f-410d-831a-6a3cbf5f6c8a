# DANFU Backend API

A complete Node.js backend with MongoDB integration for the DANFU e-commerce platform.

## 🚀 Features

- **RESTful API** with Express.js
- **MongoDB** database with Mongoose ODM
- **JWT Authentication** with refresh tokens
- **Real-time updates** with Socket.io
- **Role-based access control** (<PERSON><PERSON>, Se<PERSON>, Customer)
- **Product management** with categories and reviews
- **Shopping cart** and wishlist functionality
- **Order management** system
- **File upload** support (Cloudinary integration)
- **Email notifications** (<PERSON>demailer)
- **Rate limiting** and security middleware
- **Input validation** with Joi
- **Error handling** and logging

## 📋 Prerequisites

- Node.js (v16 or higher)
- MongoDB (running on localhost:27017)
- npm or yarn package manager

## 🛠️ Installation

1. **Navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   ```
   
   Update the `.env` file with your configuration:
   ```env
   PORT=5000
   NODE_ENV=development
   MONGODB_URI=mongodb://localhost:27017/Danfu
   JWT_SECRET=your-super-secret-jwt-key
   JWT_REFRESH_SECRET=your-super-secret-refresh-key
   FRONTEND_URL=http://localhost:5174
   ```

4. **Start MongoDB:**
   Make sure MongoDB is running on your system:
   ```bash
   # On Windows (if MongoDB is installed as a service)
   net start MongoDB
   
   # On macOS/Linux
   sudo systemctl start mongod
   # or
   brew services start mongodb-community
   ```

5. **Seed the database:**
   ```bash
   npm run seed
   ```

6. **Start the development server:**
   ```bash
   npm run dev
   ```

The server will start on `http://localhost:5000`

## 📚 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh-token` - Refresh JWT token
- `POST /api/auth/logout` - Logout user

### Products
- `GET /api/products` - Get all products (with filters)
- `GET /api/products/:id` - Get single product
- `POST /api/products` - Create product (Seller/Admin)
- `PUT /api/products/:id` - Update product (Seller/Admin)
- `DELETE /api/products/:id` - Delete product (Seller/Admin)
- `POST /api/products/:id/reviews` - Add product review

### Cart & Wishlist
- `GET /api/cart` - Get user's cart
- `POST /api/cart/add` - Add item to cart
- `PUT /api/cart/items/:itemId` - Update cart item
- `DELETE /api/cart/items/:itemId` - Remove cart item
- `DELETE /api/cart/clear` - Clear entire cart
- `GET /api/cart/wishlist` - Get wishlist
- `POST /api/cart/wishlist/:productId` - Add to wishlist
- `DELETE /api/cart/wishlist/:productId` - Remove from wishlist

## 🔐 Demo Accounts

The seeder creates the following demo accounts:

```
Admin Account:
Email: <EMAIL>
Password: admin123

Customer Account:
Email: <EMAIL>
Password: customer123

Seller Account:
Email: <EMAIL>
Password: seller123

Athlete Account:
Email: <EMAIL>
Password: athlete123
```

## 🌐 Real-time Features

The backend includes Socket.io for real-time updates:

- **Cart synchronization** across devices
- **Product stock updates** in real-time
- **Order status notifications**
- **New review notifications** for sellers

## 📁 Project Structure

```
backend/
├── config/
│   ├── database.js          # MongoDB connection
│   └── config.js            # App configuration
├── models/
│   ├── User.js              # User model
│   ├── Product.js           # Product model
│   ├── Category.js          # Category model
│   └── Order.js             # Order model
├── routes/
│   ├── auth.js              # Authentication routes
│   ├── products.js          # Product routes
│   └── cart.js              # Cart routes
├── controllers/
│   ├── authController.js    # Auth logic
│   ├── productController.js # Product logic
│   └── cartController.js    # Cart logic
├── middleware/
│   ├── auth.js              # Authentication middleware
│   ├── validation.js        # Input validation
│   └── errorHandler.js      # Error handling
├── seeders/
│   └── seedDatabase.js      # Database seeder
├── server.js                # Main server file
└── package.json
```

## 🧪 Testing

Run the API tests:
```bash
npm test
```

## 🔧 Development Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run seed` - Seed database with sample data
- `npm test` - Run tests

## 🚀 Production Deployment

1. Set `NODE_ENV=production` in your environment
2. Update MongoDB URI for production database
3. Set secure JWT secrets
4. Configure CORS for your frontend domain
5. Set up SSL certificates
6. Use PM2 or similar for process management

## 📝 API Documentation

The API follows RESTful conventions with consistent response formats:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "pagination": { ... } // for paginated endpoints
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": [ ... ] // for validation errors
}
```

## 🔒 Security Features

- JWT authentication with refresh tokens
- Password hashing with bcrypt
- Rate limiting on API endpoints
- Input validation and sanitization
- CORS protection
- Helmet.js security headers
- MongoDB injection protection

## 📞 Support

For issues and questions, please check the API documentation or contact the development team.
