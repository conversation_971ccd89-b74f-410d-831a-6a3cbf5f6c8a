{"name": "danfu-backend", "version": "1.0.0", "description": "DANFU E-commerce Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node seeders/seedDatabase.js", "test": "jest"}, "keywords": ["ecommerce", "api", "nodejs", "mongodb", "express"], "author": "DANFU Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "socket.io": "^4.7.4", "multer": "^1.4.5-lts.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "compression": "^1.7.4", "morgan": "^1.10.0", "express-validator": "^7.0.1", "cloudinary": "^1.41.0", "nodemailer": "^6.9.7", "crypto": "^1.0.1", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}