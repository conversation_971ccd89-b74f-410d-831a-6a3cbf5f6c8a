import { Product, User, Category, CartItem, CartState } from '../types';

// Transform backend product to frontend product
export const transformProduct = (backendProduct: any): Product => {
  return {
    id: backendProduct._id || backendProduct.id,
    name: backendProduct.name,
    description: backendProduct.description,
    shortDescription: backendProduct.shortDescription,
    price: backendProduct.price,
    originalPrice: backendProduct.originalPrice,
    discount: backendProduct.discount || 0,
    rating: backendProduct.averageRating || backendProduct.rating || 0,
    reviews: backendProduct.reviewCount || backendProduct.reviews || 0,
    averageRating: backendProduct.averageRating || 0,
    reviewCount: backendProduct.reviewCount || 0,
    image: backendProduct.images?.[0] || backendProduct.image || '',
    images: backendProduct.images || [backendProduct.image].filter(Boolean),
    badge: backendProduct.badges?.[0] || backendProduct.badge,
    badges: backendProduct.badges || (backendProduct.badge ? [backendProduct.badge] : []),
    category: typeof backendProduct.category === 'object' 
      ? backendProduct.category._id || backendProduct.category.id 
      : backendProduct.category,
    subcategory: backendProduct.subcategory,
    brand: backendProduct.brand,
    model: backendProduct.model,
    sku: backendProduct.sku,
    slug: backendProduct.slug,
    stock: backendProduct.stock || 0,
    inventory: {
      quantity: backendProduct.stock || 0,
      lowStockThreshold: backendProduct.lowStockThreshold || 10
    },
    specifications: {
      size: backendProduct.sizes || backendProduct.specifications?.size,
      color: backendProduct.colors || backendProduct.specifications?.color,
      material: backendProduct.specifications?.material,
      weight: backendProduct.weight || backendProduct.specifications?.weight,
      dimensions: backendProduct.dimensions || backendProduct.specifications?.dimensions
    },
    sizes: backendProduct.sizes || [],
    colors: backendProduct.colors || [],
    features: backendProduct.features || [],
    tags: backendProduct.tags || [],
    isActive: backendProduct.status === 'active' || backendProduct.isActive !== false,
    status: backendProduct.status || 'active',
    seller: backendProduct.seller,
    salesCount: backendProduct.salesCount || 0,
    viewCount: backendProduct.viewCount || 0,
    wishlistCount: backendProduct.wishlistCount || 0,
    createdAt: backendProduct.createdAt,
    updatedAt: backendProduct.updatedAt
  };
};

// Transform backend user to frontend user
export const transformUser = (backendUser: any): User => {
  return {
    id: backendUser._id || backendUser.id,
    email: backendUser.email,
    firstName: backendUser.firstName,
    lastName: backendUser.lastName,
    fullName: backendUser.fullName || `${backendUser.firstName} ${backendUser.lastName}`,
    phoneNumber: backendUser.phoneNumber,
    dateOfBirth: backendUser.dateOfBirth,
    avatar: backendUser.avatar,
    accountType: backendUser.accountType,
    sellerInfo: backendUser.sellerInfo ? {
      businessName: backendUser.sellerInfo.businessName,
      businessType: backendUser.sellerInfo.businessType,
      taxId: backendUser.sellerInfo.taxId,
      businessAddress: backendUser.sellerInfo.businessAddress,
      isVerified: backendUser.sellerInfo.isVerified,
      verificationDocuments: backendUser.sellerInfo.verificationDocuments || [],
      commissionRate: backendUser.sellerInfo.commissionRate,
      totalSales: backendUser.sellerInfo.totalSales || 0,
      rating: backendUser.sellerInfo.rating || 0,
      reviewCount: backendUser.sellerInfo.reviewCount || 0
    } : undefined,
    isEmailVerified: backendUser.isEmailVerified,
    isActive: backendUser.isActive,
    addresses: backendUser.addresses || [],
    preferences: backendUser.preferences || {
      notifications: true,
      marketing: false,
      newsletter: false
    },
    loyaltyPoints: backendUser.loyaltyPoints || 0,
    wishlist: backendUser.wishlist || [],
    cart: backendUser.cart || [],
    lastLogin: backendUser.lastLogin,
    loginCount: backendUser.loginCount || 0,
    createdAt: backendUser.createdAt,
    updatedAt: backendUser.updatedAt
  };
};

// Transform backend category to frontend category
export const transformCategory = (backendCategory: any): Category => {
  return {
    id: backendCategory._id || backendCategory.id,
    name: backendCategory.name,
    title: backendCategory.title || backendCategory.name,
    description: backendCategory.description,
    slug: backendCategory.slug,
    itemCount: backendCategory.productCount?.toString() || '0',
    productCount: backendCategory.productCount || 0,
    image: backendCategory.image || '',
    icon: backendCategory.icon,
    category: backendCategory.category,
    parent: backendCategory.parent,
    parentCategory: backendCategory.parent,
    children: backendCategory.children || [],
    level: backendCategory.level || 0,
    isActive: backendCategory.isActive !== false,
    isFeatured: backendCategory.isFeatured || false,
    sortOrder: backendCategory.sortOrder || 0,
    metaTitle: backendCategory.metaTitle,
    metaDescription: backendCategory.metaDescription,
    createdAt: backendCategory.createdAt,
    updatedAt: backendCategory.updatedAt
  };
};

// Transform backend cart item to frontend cart item
export const transformCartItem = (backendItem: any): CartItem => {
  return {
    id: backendItem._id || backendItem.id,
    _id: backendItem._id,
    productId: typeof backendItem.product === 'object' 
      ? backendItem.product._id || backendItem.product.id 
      : backendItem.product,
    product: typeof backendItem.product === 'object' 
      ? transformProduct(backendItem.product)
      : backendItem.product,
    quantity: backendItem.quantity,
    price: backendItem.price,
    selectedSize: backendItem.selectedSize,
    selectedColor: backendItem.selectedColor,
    seller: backendItem.seller,
    addedAt: backendItem.addedAt
  };
};

// Transform backend cart to frontend cart
export const transformCart = (backendCart: any): CartState => {
  return {
    items: backendCart.items?.map(transformCartItem) || [],
    totalItems: backendCart.totalItems || 0,
    totalAmount: backendCart.totalAmount || 0,
    isOpen: false, // This is managed by frontend state
    loading: false // This is managed by frontend state
  };
};

// Transform frontend product for backend API
export const transformProductForBackend = (frontendProduct: Partial<Product>): any => {
  return {
    name: frontendProduct.name,
    description: frontendProduct.description,
    shortDescription: frontendProduct.shortDescription,
    price: frontendProduct.price,
    originalPrice: frontendProduct.originalPrice,
    discount: frontendProduct.discount,
    stock: frontendProduct.stock || frontendProduct.inventory?.quantity,
    category: typeof frontendProduct.category === 'object' 
      ? frontendProduct.category.id 
      : frontendProduct.category,
    brand: frontendProduct.brand,
    model: frontendProduct.model,
    sku: frontendProduct.sku,
    images: frontendProduct.images || (frontendProduct.image ? [frontendProduct.image] : []),
    sizes: frontendProduct.sizes || frontendProduct.specifications?.size,
    colors: frontendProduct.colors || frontendProduct.specifications?.color,
    features: frontendProduct.features,
    tags: frontendProduct.tags,
    specifications: frontendProduct.specifications ? [
      ...(frontendProduct.specifications.material ? [{ name: 'Material', value: frontendProduct.specifications.material }] : []),
      ...(frontendProduct.specifications.weight ? [{ name: 'Weight', value: `${frontendProduct.specifications.weight}g` }] : []),
      ...(frontendProduct.specifications.dimensions ? [{ 
        name: 'Dimensions', 
        value: `${frontendProduct.specifications.dimensions.length}x${frontendProduct.specifications.dimensions.width}x${frontendProduct.specifications.dimensions.height}cm` 
      }] : [])
    ] : [],
    weight: frontendProduct.specifications?.weight,
    dimensions: frontendProduct.specifications?.dimensions
  };
};

// Transform pagination response
export const transformPaginatedResponse = <T>(
  backendResponse: any,
  transformFn: (item: any) => T
): { data: T[]; pagination: any } => {
  return {
    data: backendResponse.data?.map(transformFn) || [],
    pagination: backendResponse.pagination || {
      page: 1,
      limit: 10,
      total: 0,
      pages: 0
    }
  };
};

// Error transformation
export const transformError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.response?.data?.errors) {
    return error.response.data.errors.map((err: any) => err.message).join(', ');
  }
  
  if (error.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};
