require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const User = require('../models/User');
const Category = require('../models/Category');
const Product = require('../models/Product');
const connectDB = require('../config/database');

// Sample data
const categories = [
  {
    name: 'Athletic Footwear',
    description: 'High-performance shoes for all sports and activities',
    slug: 'athletic-footwear',
    image: 'https://images.unsplash.com/photo-**********-7eec264c27ff?w=500',
    isFeatured: true,
    sortOrder: 1
  },
  {
    name: 'Sportswear',
    description: 'Comfortable and stylish athletic clothing',
    slug: 'sportswear',
    image: 'https://images.unsplash.com/photo-*************-1cb2f99b2d8b?w=500',
    isFeatured: true,
    sortOrder: 2
  },
  {
    name: 'Fitness Equipment',
    description: 'Professional-grade fitness and training equipment',
    slug: 'fitness-equipment',
    image: 'https://images.unsplash.com/photo-*************-1cb2f99b2d8b?w=500',
    isFeatured: true,
    sortOrder: 3
  },
  {
    name: 'Accessories',
    description: 'Essential sports and fitness accessories',
    slug: 'accessories',
    image: 'https://images.unsplash.com/photo-*************-1cb2f99b2d8b?w=500',
    isFeatured: false,
    sortOrder: 4
  }
];

const users = [
  {
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'admin123',
    accountType: 'admin',
    isEmailVerified: true,
    isActive: true
  },
  {
    firstName: 'John',
    lastName: 'Customer',
    email: '<EMAIL>',
    password: 'customer123',
    accountType: 'customer',
    isEmailVerified: true,
    isActive: true
  },
  {
    firstName: 'Mike',
    lastName: 'Store',
    email: '<EMAIL>',
    password: 'seller123',
    accountType: 'seller',
    isEmailVerified: true,
    isActive: true,
    sellerInfo: {
      businessName: "Mike's Sports Store",
      businessType: 'retail',
      taxId: '*********',
      businessAddress: {
        street: '123 Business St',
        city: 'Commerce City',
        state: 'CA',
        zipCode: '90210',
        country: 'USA'
      },
      isVerified: true,
      verificationDocuments: ['business_license.pdf', 'tax_id.pdf'],
      commissionRate: 0.15
    }
  },
  {
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    password: 'athlete123',
    accountType: 'customer',
    isEmailVerified: true,
    isActive: true
  }
];

const generateProducts = (categories, sellers) => {
  const products = [];
  const brands = ['Nike', 'Adidas', 'Puma', 'Under Armour', 'Reebok', 'New Balance'];
  const productTemplates = [
    {
      name: 'Air Max Running Shoes',
      description: 'Premium running shoes with advanced cushioning technology for maximum comfort and performance.',
      shortDescription: 'Premium running shoes with advanced cushioning',
      price: 129.99,
      originalPrice: 159.99,
      discount: 19,
      stock: 50,
      images: [
        'https://images.unsplash.com/photo-**********-7eec264c27ff?w=800',
        'https://images.unsplash.com/photo-**********-b41d501d3772?w=800'
      ],
      sizes: ['7', '8', '9', '10', '11', '12'],
      colors: ['Black', 'White', 'Gray'],
      features: ['Breathable mesh upper', 'Responsive cushioning', 'Durable rubber outsole'],
      tags: ['running', 'athletic', 'comfortable'],
      badges: ['bestseller', 'featured']
    },
    {
      name: 'Performance Training T-Shirt',
      description: 'Moisture-wicking athletic t-shirt designed for intense training sessions and everyday wear.',
      shortDescription: 'Moisture-wicking athletic t-shirt',
      price: 29.99,
      originalPrice: 39.99,
      discount: 25,
      stock: 100,
      images: [
        'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=800',
        'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=800'
      ],
      sizes: ['S', 'M', 'L', 'XL', 'XXL'],
      colors: ['Black', 'White', 'Gray', 'Navy'],
      features: ['Moisture-wicking fabric', 'Quick-dry technology', 'Comfortable fit'],
      tags: ['training', 'shirt', 'athletic'],
      badges: ['new', 'sale']
    },
    {
      name: 'Professional Yoga Mat',
      description: 'High-quality yoga mat with superior grip and cushioning for all types of yoga practice.',
      shortDescription: 'High-quality yoga mat with superior grip',
      price: 49.99,
      stock: 30,
      images: [
        'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800'
      ],
      colors: ['Purple', 'Blue', 'Green', 'Pink'],
      features: ['Non-slip surface', 'Extra cushioning', 'Eco-friendly materials'],
      tags: ['yoga', 'fitness', 'mat'],
      badges: ['featured']
    }
  ];

  // Generate products for each category and seller
  categories.forEach((category, catIndex) => {
    sellers.forEach((seller, sellerIndex) => {
      productTemplates.forEach((template, templateIndex) => {
        const brand = brands[Math.floor(Math.random() * brands.length)];
        const product = {
          ...template,
          name: `${brand} ${template.name}`,
          brand,
          category: category._id,
          seller: seller._id,
          sku: `SKU-${catIndex}${sellerIndex}${templateIndex}-${Date.now()}`,
          averageRating: 3.5 + Math.random() * 1.5,
          reviewCount: Math.floor(Math.random() * 50),
          salesCount: Math.floor(Math.random() * 100),
          viewCount: Math.floor(Math.random() * 500),
          specifications: [
            { name: 'Material', value: 'Premium synthetic' },
            { name: 'Weight', value: '300g' },
            { name: 'Origin', value: 'Made in USA' }
          ]
        };
        products.push(product);
      });
    });
  });

  return products;
};

const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Connect to database
    await connectDB();

    // Clear existing data
    console.log('🗑️  Clearing existing data...');
    await User.deleteMany({});
    await Category.deleteMany({});
    await Product.deleteMany({});

    // Create categories
    console.log('📂 Creating categories...');
    const createdCategories = await Category.insertMany(categories);
    console.log(`✅ Created ${createdCategories.length} categories`);

    // Hash passwords and create users
    console.log('👥 Creating users...');
    const hashedUsers = await Promise.all(
      users.map(async (user) => ({
        ...user,
        password: await bcrypt.hash(user.password, 12)
      }))
    );
    const createdUsers = await User.insertMany(hashedUsers);
    console.log(`✅ Created ${createdUsers.length} users`);

    // Get sellers for product creation
    const sellers = createdUsers.filter(user => 
      user.accountType === 'seller' || user.accountType === 'admin'
    );

    // Generate and create products
    console.log('🛍️  Creating products...');
    const productData = generateProducts(createdCategories, sellers);
    const createdProducts = await Product.insertMany(productData);
    console.log(`✅ Created ${createdProducts.length} products`);

    // Update category product counts
    console.log('📊 Updating category statistics...');
    for (const category of createdCategories) {
      const productCount = await Product.countDocuments({ category: category._id });
      await Category.findByIdAndUpdate(category._id, { productCount });
    }

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📋 Demo Accounts:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Customer: <EMAIL> / customer123');
    console.log('Seller: <EMAIL> / seller123');
    console.log('Athlete: <EMAIL> / athlete123');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
};

// Run seeder if called directly
if (require.main === module) {
  seedDatabase();
}

module.exports = seedDatabase;
