#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 DANFU - Complete Setup Script\n');

// Check if MongoDB is running
const checkMongoDB = () => {
  return new Promise((resolve) => {
    const mongoose = require('mongoose');
    mongoose.connect('mongodb://localhost:27017/Danfu', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 3000
    })
    .then(() => {
      console.log('✅ MongoDB connection successful');
      mongoose.connection.close();
      resolve(true);
    })
    .catch(() => {
      console.log('❌ MongoDB connection failed');
      console.log('Please make sure MongoDB is running on localhost:27017');
      resolve(false);
    });
  });
};

// Install frontend dependencies
const installFrontendDeps = () => {
  return new Promise((resolve, reject) => {
    console.log('📦 Installing frontend dependencies...');
    
    const npm = spawn('npm', ['install'], {
      stdio: 'inherit',
      shell: true,
      cwd: __dirname
    });

    npm.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Frontend dependencies installed');
        resolve();
      } else {
        reject(new Error('Failed to install frontend dependencies'));
      }
    });
  });
};

// Install backend dependencies
const installBackendDeps = () => {
  return new Promise((resolve, reject) => {
    console.log('📦 Installing backend dependencies...');
    
    const npm = spawn('npm', ['install'], {
      stdio: 'inherit',
      shell: true,
      cwd: path.join(__dirname, 'backend')
    });

    npm.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Backend dependencies installed');
        resolve();
      } else {
        reject(new Error('Failed to install backend dependencies'));
      }
    });
  });
};

// Seed database
const seedDatabase = () => {
  return new Promise((resolve, reject) => {
    console.log('🌱 Seeding database with sample data...');
    
    const seed = spawn('npm', ['run', 'seed'], {
      stdio: 'inherit',
      shell: true,
      cwd: path.join(__dirname, 'backend')
    });

    seed.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Database seeded successfully');
        resolve();
      } else {
        console.log('⚠️  Database seeding failed, but continuing...');
        resolve(); // Continue even if seeding fails
      }
    });
  });
};

// Start backend server
const startBackend = () => {
  console.log('🚀 Starting backend server...');
  
  const backend = spawn('npm', ['run', 'dev'], {
    stdio: 'pipe',
    shell: true,
    cwd: path.join(__dirname, 'backend')
  });

  backend.stdout.on('data', (data) => {
    console.log(`[Backend] ${data.toString().trim()}`);
  });

  backend.stderr.on('data', (data) => {
    console.error(`[Backend Error] ${data.toString().trim()}`);
  });

  return backend;
};

// Start frontend server
const startFrontend = () => {
  console.log('🚀 Starting frontend server...');
  
  const frontend = spawn('npm', ['run', 'dev'], {
    stdio: 'pipe',
    shell: true,
    cwd: __dirname
  });

  frontend.stdout.on('data', (data) => {
    console.log(`[Frontend] ${data.toString().trim()}`);
  });

  frontend.stderr.on('data', (data) => {
    console.error(`[Frontend Error] ${data.toString().trim()}`);
  });

  return frontend;
};

// Create environment file if it doesn't exist
const createEnvFile = () => {
  const envPath = path.join(__dirname, 'backend', '.env');
  
  if (!fs.existsSync(envPath)) {
    console.log('📝 Creating backend .env file...');
    
    const envContent = `# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/Danfu

# JWT Configuration
JWT_SECRET=danfu-super-secret-jwt-key-change-in-production
JWT_REFRESH_SECRET=danfu-super-secret-refresh-key-change-in-production
JWT_EXPIRE=24h
JWT_REFRESH_EXPIRE=7d

# CORS Configuration
FRONTEND_URL=http://localhost:5174

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_SALT_ROUNDS=12
`;
    
    fs.writeFileSync(envPath, envContent);
    console.log('✅ Backend .env file created');
  }
};

// Main setup function
const main = async () => {
  try {
    console.log('🎯 Setting up DANFU E-commerce Platform...\n');

    // Create env file
    createEnvFile();

    // Check MongoDB
    const mongoConnected = await checkMongoDB();
    if (!mongoConnected) {
      console.log('\n💡 To start MongoDB:');
      console.log('   Windows: net start MongoDB');
      console.log('   macOS: brew services start mongodb-community');
      console.log('   Linux: sudo systemctl start mongod\n');
      process.exit(1);
    }

    // Install dependencies
    await installFrontendDeps();
    await installBackendDeps();

    // Seed database
    await seedDatabase();

    console.log('\n🎉 Setup completed successfully!\n');
    console.log('📋 Demo Accounts:');
    console.log('   Admin: <EMAIL> / admin123');
    console.log('   Customer: <EMAIL> / customer123');
    console.log('   Seller: <EMAIL> / seller123');
    console.log('   Athlete: <EMAIL> / athlete123\n');

    console.log('🌐 Starting servers...\n');

    // Start both servers
    const backendProcess = startBackend();
    
    // Wait a bit for backend to start
    setTimeout(() => {
      const frontendProcess = startFrontend();
      
      console.log('\n🎯 DANFU is now running!');
      console.log('   Frontend: http://localhost:5174');
      console.log('   Backend API: http://localhost:5000');
      console.log('   API Health: http://localhost:5000/health\n');
      console.log('Press Ctrl+C to stop both servers\n');

      // Handle graceful shutdown
      process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down servers...');
        backendProcess.kill('SIGINT');
        frontendProcess.kill('SIGINT');
        process.exit(0);
      });
    }, 3000);

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
};

main();
