const Joi = require('joi');

// Validation schemas
const schemas = {
  // User registration
  register: Joi.object({
    firstName: Joi.string().min(2).max(50).required(),
    lastName: Joi.string().min(2).max(50).required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(6).max(128).required(),
    confirmPassword: Joi.string().valid(Joi.ref('password')).required(),
    phoneNumber: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).optional(),
    dateOfBirth: Joi.date().max('now').optional(),
    accountType: Joi.string().valid('customer', 'seller').default('customer'),
    acceptTerms: Joi.boolean().valid(true).required(),
    subscribeNewsletter: Joi.boolean().default(false),
    // Seller specific fields
    businessName: Joi.when('accountType', {
      is: 'seller',
      then: Joi.string().min(2).max(100).required(),
      otherwise: Joi.optional()
    }),
    businessType: Joi.when('accountType', {
      is: 'seller',
      then: Joi.string().valid('retail', 'wholesale', 'manufacturer', 'individual').required(),
      otherwise: Joi.optional()
    }),
    taxId: Joi.when('accountType', {
      is: 'seller',
      then: Joi.string().optional(),
      otherwise: Joi.optional()
    })
  }),

  // User login
  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),

  // Product creation
  createProduct: Joi.object({
    name: Joi.string().min(2).max(200).required(),
    description: Joi.string().min(10).max(5000).required(),
    shortDescription: Joi.string().max(200).optional(),
    price: Joi.number().min(0).required(),
    originalPrice: Joi.number().min(0).optional(),
    discount: Joi.number().min(0).max(100).default(0),
    stock: Joi.number().min(0).required(),
    category: Joi.string().hex().length(24).required(),
    brand: Joi.string().min(1).max(100).required(),
    model: Joi.string().max(100).optional(),
    sku: Joi.string().min(1).max(50).required(),
    images: Joi.array().items(Joi.string().uri()).min(1).required(),
    videos: Joi.array().items(Joi.string().uri()).optional(),
    sizes: Joi.array().items(Joi.string()).optional(),
    colors: Joi.array().items(Joi.string()).optional(),
    specifications: Joi.array().items(
      Joi.object({
        name: Joi.string().required(),
        value: Joi.string().required()
      })
    ).optional(),
    features: Joi.array().items(Joi.string()).optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    weight: Joi.number().min(0).optional(),
    dimensions: Joi.object({
      length: Joi.number().min(0),
      width: Joi.number().min(0),
      height: Joi.number().min(0)
    }).optional()
  }),

  // Category creation
  createCategory: Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().min(10).max(1000).required(),
    parent: Joi.string().hex().length(24).optional(),
    image: Joi.string().uri().optional(),
    icon: Joi.string().optional(),
    metaTitle: Joi.string().max(60).optional(),
    metaDescription: Joi.string().max(160).optional(),
    sortOrder: Joi.number().default(0)
  }),

  // Review creation
  createReview: Joi.object({
    rating: Joi.number().min(1).max(5).required(),
    comment: Joi.string().min(10).max(1000).required()
  }),

  // Cart operations
  addToCart: Joi.object({
    productId: Joi.string().hex().length(24).required(),
    quantity: Joi.number().min(1).max(100).required(),
    selectedSize: Joi.string().optional(),
    selectedColor: Joi.string().optional()
  }),

  updateCartItem: Joi.object({
    quantity: Joi.number().min(1).max(100).required()
  }),

  // Order creation
  createOrder: Joi.object({
    items: Joi.array().items(
      Joi.object({
        product: Joi.string().hex().length(24).required(),
        quantity: Joi.number().min(1).required(),
        selectedSize: Joi.string().optional(),
        selectedColor: Joi.string().optional()
      })
    ).min(1).required(),
    shippingAddress: Joi.object({
      firstName: Joi.string().required(),
      lastName: Joi.string().required(),
      street: Joi.string().required(),
      city: Joi.string().required(),
      state: Joi.string().required(),
      zipCode: Joi.string().required(),
      country: Joi.string().default('USA'),
      phoneNumber: Joi.string().optional()
    }).required(),
    billingAddress: Joi.object({
      firstName: Joi.string().required(),
      lastName: Joi.string().required(),
      street: Joi.string().required(),
      city: Joi.string().required(),
      state: Joi.string().required(),
      zipCode: Joi.string().required(),
      country: Joi.string().default('USA'),
      phoneNumber: Joi.string().optional()
    }).optional(),
    paymentMethod: Joi.string().valid('credit_card', 'debit_card', 'paypal', 'stripe', 'cash_on_delivery').required(),
    shippingMethod: Joi.string().valid('standard', 'express', 'overnight', 'pickup').default('standard'),
    customerNotes: Joi.string().max(500).optional(),
    couponCode: Joi.string().optional()
  }),

  // Profile update
  updateProfile: Joi.object({
    firstName: Joi.string().min(2).max(50).optional(),
    lastName: Joi.string().min(2).max(50).optional(),
    phoneNumber: Joi.string().pattern(/^\+?[\d\s\-\(\)]+$/).optional(),
    dateOfBirth: Joi.date().max('now').optional(),
    preferences: Joi.object({
      notifications: Joi.boolean().optional(),
      marketing: Joi.boolean().optional(),
      newsletter: Joi.boolean().optional()
    }).optional()
  })
};

// Validation middleware factory
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schemas[schema].validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      });
    }

    req.validatedData = value;
    next();
  };
};

// Query parameter validation
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        message: 'Query validation error',
        errors
      });
    }

    req.validatedQuery = value;
    next();
  };
};

// Common query schemas
const querySchemas = {
  pagination: Joi.object({
    page: Joi.number().min(1).default(1),
    limit: Joi.number().min(1).max(100).default(10),
    sort: Joi.string().optional(),
    order: Joi.string().valid('asc', 'desc').default('desc')
  }),

  productFilters: Joi.object({
    page: Joi.number().min(1).default(1),
    limit: Joi.number().min(1).max(100).default(10),
    sort: Joi.string().valid('name', 'price', 'rating', 'createdAt', 'salesCount').default('createdAt'),
    order: Joi.string().valid('asc', 'desc').default('desc'),
    category: Joi.string().hex().length(24).optional(),
    minPrice: Joi.number().min(0).optional(),
    maxPrice: Joi.number().min(0).optional(),
    brand: Joi.string().optional(),
    search: Joi.string().min(1).max(100).optional(),
    inStock: Joi.boolean().optional(),
    featured: Joi.boolean().optional()
  })
};

module.exports = {
  validate,
  validateQuery,
  querySchemas
};
