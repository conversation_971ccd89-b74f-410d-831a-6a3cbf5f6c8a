import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Play, Volume2, VolumeX, Sparkles } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const HeroSection = () => {
  const [isMuted, setIsMuted] = useState(true);
  const [currentVideo, setCurrentVideo] = useState(0);
  const [showParticles, setShowParticles] = useState(false);

  const videos = [
    "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
    "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4"
  ];

  useEffect(() => {
    setShowParticles(true);
    const interval = setInterval(() => {
      setCurrentVideo((prev) => (prev + 1) % videos.length);
    }, 8000);
    return () => clearInterval(interval);
  }, []);

  const floatingElements = Array.from({ length: 20 }, (_, i) => ({
    id: i,
    x: Math.random() * 100,
    y: Math.random() * 100,
    delay: Math.random() * 2,
    duration: 3 + Math.random() * 2,
  }));

  return (
    <section className="min-h-screen flex items-center relative overflow-hidden" style={{ backgroundColor: '#2a2a2a', color: '#f8fafc' }}>
      {/* Video Background */}
      <div className="absolute inset-0 z-0">
        <AnimatePresence mode="wait">
          <motion.video
            key={currentVideo}
            autoPlay
            loop
            muted={isMuted}
            playsInline
            className="w-full h-full object-cover opacity-30"
            initial={{ opacity: 0, scale: 1.1 }}
            animate={{ opacity: 0.3, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 1.5 }}
          >
            <source src={videos[currentVideo]} type="video/mp4" />
          </motion.video>
        </AnimatePresence>

        {/* Video overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-transparent to-black/60"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
      </div>

      {/* Floating Particles */}
      {showParticles && (
        <div className="absolute inset-0 z-10 pointer-events-none">
          {floatingElements.map((element) => (
            <motion.div
              key={element.id}
              className="absolute w-2 h-2 rounded-full"
              style={{
                backgroundColor: '#ef4444',
                left: `${element.x}%`,
                top: `${element.y}%`,
                filter: 'blur(1px)'
              }}
              animate={{
                y: [-20, -100, -20],
                opacity: [0, 1, 0],
                scale: [0, 1, 0]
              }}
              transition={{
                duration: element.duration,
                delay: element.delay,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      )}

      {/* Animated Background Elements */}
      <div className="absolute inset-0 z-10 opacity-20">
        <motion.div
          className="absolute top-20 left-20 w-32 h-32 rounded-full blur-3xl"
          style={{ backgroundColor: '#ef4444' }}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-40 h-40 rounded-full blur-3xl"
          style={{ backgroundColor: '#ef4444' }}
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.5, 0.3]
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 w-24 h-24 rounded-full blur-2xl"
          style={{ backgroundColor: '#ef4444', transform: 'translate(-50%, -50%)' }}
          animate={{
            scale: [0.8, 1.5, 0.8],
            opacity: [0.1, 0.3, 0.1]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
      </div>

      {/* Video Controls */}
      <motion.button
        onClick={() => setIsMuted(!isMuted)}
        className="absolute top-24 right-6 z-30 p-3 rounded-full backdrop-blur-sm transition-all duration-300"
        style={{ backgroundColor: 'rgba(26, 26, 26, 0.7)' }}
        whileHover={{ scale: 1.1, backgroundColor: 'rgba(239, 68, 68, 0.8)' }}
        whileTap={{ scale: 0.9 }}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 1 }}
      >
        {isMuted ? (
          <VolumeX className="h-5 w-5 text-white" />
        ) : (
          <Volume2 className="h-5 w-5 text-white" />
        )}
      </motion.button>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-20">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <motion.span
              className="inline-flex items-center px-6 py-3 rounded-full text-sm font-medium border"
              style={{ backgroundColor: 'rgba(239, 68, 68, 0.1)', borderColor: '#ef4444', color: '#ef4444' }}
              whileHover={{ scale: 1.05, backgroundColor: 'rgba(239, 68, 68, 0.2)' }}
              animate={{
                boxShadow: [
                  '0 0 0 0 rgba(239, 68, 68, 0.4)',
                  '0 0 0 10px rgba(239, 68, 68, 0)',
                  '0 0 0 0 rgba(239, 68, 68, 0)'
                ]
              }}
              transition={{
                boxShadow: {
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }
              }}
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
              >
                <Sparkles className="h-4 w-4 mr-2" />
              </motion.div>
              ✨ New Collection 2024 ✨
            </motion.span>
          </motion.div>

          <motion.h1
            className="text-6xl lg:text-8xl font-bold leading-tight mb-8"
            style={{ color: '#f8fafc' }}
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.4 }}
          >
            <motion.span
              className="block"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              Elevate Your
            </motion.span>
            <motion.span
              className="block"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              Athletic
            </motion.span>
            <motion.span
              className="block bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 1 }}
              whileHover={{
                scale: 1.05,
                filter: "drop-shadow(0 0 20px rgba(239, 68, 68, 0.5))"
              }}
            >
              Performance
            </motion.span>
          </motion.h1>

          <motion.p
            className="text-xl max-w-2xl mx-auto mb-12"
            style={{ color: '#94a3b8' }}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            Unleash your potential with premium athletic gear designed for champions.
            Experience the perfect fusion of style, performance, and innovation that
            elevates every workout, every game, every moment.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.4 }}
          >
            <Link to="/shop">
              <motion.button
                className="text-white px-10 py-4 rounded-xl font-bold flex items-center justify-center gap-3 transition-all duration-300 w-full sm:w-auto text-lg shadow-2xl"
                style={{ backgroundColor: '#ef4444' }}
                whileHover={{
                  scale: 1.05,
                  backgroundColor: '#dc2626',
                  boxShadow: '0 20px 40px rgba(239, 68, 68, 0.4)'
                }}
                whileTap={{ scale: 0.95 }}
              >
                <Sparkles className="h-5 w-5" />
                Shop Collection
                <ArrowRight className="h-5 w-5" />
              </motion.button>
            </Link>
            <Link to="/about">
              <motion.button
                className="px-10 py-4 rounded-xl font-bold flex items-center justify-center gap-3 transition-all duration-300 w-full sm:w-auto text-lg backdrop-blur-sm"
                style={{
                  border: '2px solid rgba(239, 68, 68, 0.5)',
                  color: '#f8fafc',
                  backgroundColor: 'rgba(239, 68, 68, 0.1)'
                }}
                whileHover={{
                  scale: 1.05,
                  borderColor: '#ef4444',
                  backgroundColor: 'rgba(239, 68, 68, 0.2)'
                }}
                whileTap={{ scale: 0.95 }}
              >
                <Play className="h-5 w-5" />
                Watch Story
              </motion.button>
            </Link>
          </motion.div>


        </div>

        {/* Right Content - Hero Image */}
        <div className="relative">
          <div className="bg-gray-900 rounded-3xl p-8 relative overflow-hidden">
            {/* Geometric shapes */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-red-500 opacity-20 rounded-full -translate-y-16 translate-x-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-red-500 opacity-20 rounded-full translate-y-12 -translate-x-12"></div>
            
            {/* Model placeholder */}
            <div className="relative z-10 h-96 flex items-center justify-center">
              <div className="text-center">
                <div className="w-48 h-48 bg-gradient-to-br from-red-500 to-red-700 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-4xl font-bold">DANFU</span>
                </div>
                <p className="text-gray-400">Premium Athletic Wear</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;