import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Minus, ShoppingBag, Trash2 } from 'lucide-react';
import { useCart } from '../context/CartContext';

const CartSidebar = () => {
  const { cart, removeFromCart, updateQuantity, toggleCart, clearCart } = useCart();

  const sidebarVariants = {
    hidden: { x: '100%', opacity: 0 },
    visible: { x: 0, opacity: 1 },
    exit: { x: '100%', opacity: 0 }
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  return (
    <AnimatePresence>
      {cart.isOpen && (
        <>
          {/* Overlay */}
          <motion.div
            className="fixed inset-0 z-40"
            style={{ backgroundColor: 'rgba(42, 42, 42, 0.8)' }}
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={toggleCart}
          />

          {/* Sidebar */}
          <motion.div
            className="fixed right-0 top-0 h-full w-full max-w-md z-50 border-l flex flex-col"
            style={{ backgroundColor: 'rgb(26, 26, 26)', borderColor: '#4a4a4a' }}
            variants={sidebarVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b" style={{ borderColor: '#4a4a4a' }}>
              <div className="flex items-center gap-3">
                <ShoppingBag className="h-6 w-6" style={{ color: '#ef4444' }} />
                <h2 className="text-xl font-bold" style={{ color: '#f8fafc' }}>
                  Shopping Cart
                </h2>
                <span 
                  className="px-2 py-1 rounded-full text-xs font-medium"
                  style={{ backgroundColor: '#ef4444', color: '#ffffff' }}
                >
                  {cart.totalItems}
                </span>
              </div>
              <motion.button
                onClick={toggleCart}
                className="p-2 rounded-full transition-colors"
                style={{ color: '#94a3b8' }}
                whileHover={{ scale: 1.1, color: '#ef4444' }}
                whileTap={{ scale: 0.9 }}
              >
                <X className="h-5 w-5" />
              </motion.button>
            </div>

            {/* Cart Items */}
            <div className="flex-1 overflow-y-auto p-6">
              {cart.items.length === 0 ? (
                <motion.div
                  className="flex flex-col items-center justify-center h-full text-center"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  <ShoppingBag className="h-16 w-16 mb-4" style={{ color: '#4a4a4a' }} />
                  <h3 className="text-lg font-medium mb-2" style={{ color: '#f8fafc' }}>
                    Your cart is empty
                  </h3>
                  <p style={{ color: '#94a3b8' }}>
                    Add some products to get started
                  </p>
                </motion.div>
              ) : (
                <div className="space-y-4">
                  <AnimatePresence>
                    {cart.items.map((item, index) => (
                      <motion.div
                        key={item.id}
                        className="flex gap-4 p-4 rounded-lg border"
                        style={{ backgroundColor: 'rgb(42, 42, 42)', borderColor: '#4a4a4a' }}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ delay: index * 0.1 }}
                        layout
                      >
                        {/* Product Image */}
                        <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src={item.product.image}
                            alt={item.product.name}
                            className="w-full h-full object-cover"
                          />
                        </div>

                        {/* Product Details */}
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm line-clamp-2 mb-1" style={{ color: '#f8fafc' }}>
                            {item.product.name}
                          </h4>
                          
                          {/* Options */}
                          {(item.selectedSize || item.selectedColor) && (
                            <div className="flex gap-2 mb-2">
                              {item.selectedSize && (
                                <span className="text-xs px-2 py-1 rounded" style={{ backgroundColor: '#4a4a4a', color: '#94a3b8' }}>
                                  Size: {item.selectedSize}
                                </span>
                              )}
                              {item.selectedColor && (
                                <span className="text-xs px-2 py-1 rounded" style={{ backgroundColor: '#4a4a4a', color: '#94a3b8' }}>
                                  Color: {item.selectedColor}
                                </span>
                              )}
                            </div>
                          )}

                          {/* Price */}
                          <div className="flex items-center justify-between">
                            <span className="font-bold" style={{ color: '#ef4444' }}>
                              ${(item.product.price * item.quantity).toFixed(2)}
                            </span>
                            
                            {/* Quantity Controls */}
                            <div className="flex items-center gap-2">
                              <motion.button
                                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                className="p-1 rounded transition-colors"
                                style={{ backgroundColor: '#4a4a4a', color: '#94a3b8' }}
                                whileHover={{ scale: 1.1, backgroundColor: '#ef4444', color: '#ffffff' }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <Minus className="h-3 w-3" />
                              </motion.button>
                              
                              <span className="w-8 text-center text-sm font-medium" style={{ color: '#f8fafc' }}>
                                {item.quantity}
                              </span>
                              
                              <motion.button
                                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                className="p-1 rounded transition-colors"
                                style={{ backgroundColor: '#4a4a4a', color: '#94a3b8' }}
                                whileHover={{ scale: 1.1, backgroundColor: '#ef4444', color: '#ffffff' }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <Plus className="h-3 w-3" />
                              </motion.button>
                              
                              <motion.button
                                onClick={() => removeFromCart(item.id)}
                                className="p-1 rounded transition-colors ml-2"
                                style={{ color: '#94a3b8' }}
                                whileHover={{ scale: 1.1, color: '#ef4444' }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </motion.button>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>

                  {/* Clear Cart Button */}
                  {cart.items.length > 0 && (
                    <motion.button
                      onClick={clearCart}
                      className="w-full py-2 text-sm rounded-lg border transition-colors"
                      style={{ borderColor: '#4a4a4a', color: '#94a3b8' }}
                      whileHover={{ borderColor: '#ef4444', color: '#ef4444' }}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.3 }}
                    >
                      Clear Cart
                    </motion.button>
                  )}
                </div>
              )}
            </div>

            {/* Footer */}
            {cart.items.length > 0 && (
              <motion.div
                className="border-t p-6 space-y-4"
                style={{ borderColor: '#4a4a4a' }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                {/* Total */}
                <div className="flex items-center justify-between text-lg font-bold">
                  <span style={{ color: '#f8fafc' }}>Total:</span>
                  <span style={{ color: '#ef4444' }}>${cart.totalAmount.toFixed(2)}</span>
                </div>

                {/* Checkout Button */}
                <motion.button
                  className="w-full py-3 rounded-lg font-semibold transition-colors"
                  style={{ backgroundColor: '#ef4444', color: '#ffffff' }}
                  whileHover={{ scale: 1.02, backgroundColor: '#dc2626' }}
                  whileTap={{ scale: 0.98 }}
                >
                  Proceed to Checkout
                </motion.button>

                {/* Continue Shopping */}
                <motion.button
                  onClick={toggleCart}
                  className="w-full py-2 text-sm transition-colors"
                  style={{ color: '#94a3b8' }}
                  whileHover={{ color: '#f8fafc' }}
                >
                  Continue Shopping
                </motion.button>
              </motion.div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default CartSidebar;
