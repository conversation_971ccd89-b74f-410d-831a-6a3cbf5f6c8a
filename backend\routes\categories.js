const express = require('express');
const {
  getCategories,
  getCategoryTree,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  getFeaturedCategories
} = require('../controllers/categoryController');
const { validate } = require('../middleware/validation');
const { verifyToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.get('/', getCategories);
router.get('/tree', getCategoryTree);
router.get('/featured', getFeaturedCategories);
router.get('/:id', getCategory);

// Protected routes (admin only)
router.use(verifyToken);
router.use(requireAdmin);

router.post('/', validate('createCategory'), createCategory);
router.put('/:id', validate('createCategory'), updateCategory);
router.delete('/:id', deleteCategory);

module.exports = router;
