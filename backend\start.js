#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting DANFU Backend Server...\n');

// Check if MongoDB is running
const checkMongoDB = () => {
  return new Promise((resolve) => {
    const mongoose = require('mongoose');
    mongoose.connect('mongodb://localhost:27017/Danfu', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 3000
    })
    .then(() => {
      console.log('✅ MongoDB connection successful');
      mongoose.connection.close();
      resolve(true);
    })
    .catch(() => {
      console.log('❌ MongoDB connection failed');
      console.log('Please make sure MongoDB is running on localhost:27017');
      resolve(false);
    });
  });
};

// Install dependencies if needed
const installDependencies = () => {
  return new Promise((resolve, reject) => {
    console.log('📦 Checking dependencies...');
    
    const npm = spawn('npm', ['install'], {
      stdio: 'inherit',
      shell: true,
      cwd: __dirname
    });

    npm.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Dependencies installed successfully');
        resolve();
      } else {
        reject(new Error('Failed to install dependencies'));
      }
    });
  });
};

// Seed database
const seedDatabase = () => {
  return new Promise((resolve, reject) => {
    console.log('🌱 Seeding database...');
    
    const seed = spawn('npm', ['run', 'seed'], {
      stdio: 'inherit',
      shell: true,
      cwd: __dirname
    });

    seed.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Database seeded successfully');
        resolve();
      } else {
        console.log('⚠️  Database seeding failed, but continuing...');
        resolve(); // Continue even if seeding fails
      }
    });
  });
};

// Start server
const startServer = () => {
  console.log('🚀 Starting development server...\n');
  
  const server = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
  });

  server.on('close', (code) => {
    console.log(`Server exited with code ${code}`);
  });

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.kill('SIGINT');
    process.exit(0);
  });
};

// Main startup sequence
const main = async () => {
  try {
    // Check MongoDB connection
    const mongoConnected = await checkMongoDB();
    if (!mongoConnected) {
      console.log('\n💡 To start MongoDB:');
      console.log('   Windows: net start MongoDB');
      console.log('   macOS: brew services start mongodb-community');
      console.log('   Linux: sudo systemctl start mongod\n');
      process.exit(1);
    }

    // Install dependencies
    await installDependencies();

    // Seed database
    await seedDatabase();

    // Start server
    startServer();

  } catch (error) {
    console.error('❌ Startup failed:', error.message);
    process.exit(1);
  }
};

main();
