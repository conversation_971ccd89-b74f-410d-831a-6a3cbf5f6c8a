const mongoose = require('mongoose');

const variantSchema = new mongoose.Schema({
  size: String,
  color: String,
  stock: { type: Number, required: true, min: 0 },
  price: { type: Number, required: true, min: 0 },
  sku: { type: String, unique: true, sparse: true }
});

const reviewSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  rating: { type: Number, required: true, min: 1, max: 5 },
  comment: { type: String, required: true },
  verified: { type: Boolean, default: false },
  helpful: { type: Number, default: 0 },
  createdAt: { type: Date, default: Date.now }
});

const productSchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true },
  description: { type: String, required: true },
  shortDescription: { type: String, maxlength: 200 },
  
  // Pricing
  price: { type: Number, required: true, min: 0 },
  originalPrice: { type: Number, min: 0 },
  discount: { type: Number, min: 0, max: 100, default: 0 },
  
  // Inventory
  stock: { type: Number, required: true, min: 0 },
  lowStockThreshold: { type: Number, default: 10 },
  
  // Product details
  category: { type: mongoose.Schema.Types.ObjectId, ref: 'Category', required: true },
  subcategory: String,
  brand: { type: String, required: true },
  model: String,
  sku: { type: String, unique: true, required: true },
  
  // Media
  images: [{ type: String, required: true }],
  videos: [String],
  
  // Variants (sizes, colors, etc.)
  variants: [variantSchema],
  sizes: [String],
  colors: [String],
  
  // Product attributes
  specifications: [{
    name: String,
    value: String
  }],
  
  features: [String],
  tags: [String],
  
  // SEO
  slug: { type: String, unique: true, required: true },
  metaTitle: String,
  metaDescription: String,
  
  // Seller information
  seller: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  
  // Status
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'out_of_stock', 'discontinued'], 
    default: 'active' 
  },
  
  // Reviews and ratings
  reviews: [reviewSchema],
  averageRating: { type: Number, default: 0, min: 0, max: 5 },
  reviewCount: { type: Number, default: 0 },
  
  // Sales data
  salesCount: { type: Number, default: 0 },
  viewCount: { type: Number, default: 0 },
  wishlistCount: { type: Number, default: 0 },
  
  // Badges and labels
  badges: [{ 
    type: String, 
    enum: ['new', 'bestseller', 'featured', 'sale', 'limited'] 
  }],
  
  // Shipping
  weight: Number,
  dimensions: {
    length: Number,
    width: Number,
    height: Number
  },
  
  shippingClass: { 
    type: String, 
    enum: ['standard', 'heavy', 'fragile', 'express'], 
    default: 'standard' 
  },
  
  // Timestamps
  publishedAt: Date,
  featuredUntil: Date
}, {
  timestamps: true
});

// Indexes for better performance
productSchema.index({ name: 'text', description: 'text', tags: 'text' });
productSchema.index({ category: 1, status: 1 });
productSchema.index({ seller: 1, status: 1 });
productSchema.index({ price: 1 });
productSchema.index({ averageRating: -1 });
productSchema.index({ salesCount: -1 });
productSchema.index({ createdAt: -1 });
productSchema.index({ slug: 1 });

// Virtual for discounted price
productSchema.virtual('discountedPrice').get(function() {
  if (this.discount > 0) {
    return this.price * (1 - this.discount / 100);
  }
  return this.price;
});

// Virtual for stock status
productSchema.virtual('stockStatus').get(function() {
  if (this.stock === 0) return 'out_of_stock';
  if (this.stock <= this.lowStockThreshold) return 'low_stock';
  return 'in_stock';
});

// Pre-save middleware to generate slug
productSchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  next();
});

// Method to add review
productSchema.methods.addReview = function(userId, rating, comment) {
  this.reviews.push({
    user: userId,
    rating,
    comment,
    verified: false // Can be updated based on purchase history
  });
  
  this.updateRatingStats();
  return this.save();
};

// Method to update rating statistics
productSchema.methods.updateRatingStats = function() {
  if (this.reviews.length === 0) {
    this.averageRating = 0;
    this.reviewCount = 0;
    return;
  }
  
  const totalRating = this.reviews.reduce((sum, review) => sum + review.rating, 0);
  this.averageRating = totalRating / this.reviews.length;
  this.reviewCount = this.reviews.length;
};

// Method to increment view count
productSchema.methods.incrementViewCount = function() {
  this.viewCount += 1;
  return this.save();
};

// Method to update stock
productSchema.methods.updateStock = function(quantity) {
  this.stock = Math.max(0, this.stock + quantity);
  if (this.stock === 0) {
    this.status = 'out_of_stock';
  } else if (this.status === 'out_of_stock') {
    this.status = 'active';
  }
  return this.save();
};

module.exports = mongoose.model('Product', productSchema);
