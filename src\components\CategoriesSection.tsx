import React from 'react';
import { ArrowRight, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useCategories } from '../hooks/useApi';

const CategoriesSection = () => {
  const { data: categories, loading, error } = useCategories();

  if (loading) {
    return (
      <section className="py-20" style={{ backgroundColor: 'rgb(26, 26, 26)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin" style={{ color: '#ef4444' }} />
            <span className="ml-2" style={{ color: '#f8fafc' }}>Loading categories...</span>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-20" style={{ backgroundColor: 'rgb(26, 26, 26)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center" style={{ color: '#ef4444' }}>
            Error loading categories: {error}
          </div>
        </div>
      </section>
    );
  }

  const staticCategories = [
    {
      title: "Athletic Footwear",
      description: "Performance shoes for every sport",
      itemCount: "150+ items",
      image: "https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg?auto=compress&cs=tinysrgb&w=600",
      category: "sportswear"
    },
    {
      title: "Training Apparel",
      description: "Premium workout clothing",
      itemCount: "200+ items",
      image: "https://images.pexels.com/photos/8532616/pexels-photo-8532616.jpeg?auto=compress&cs=tinysrgb&w=600",
      category: "sportswear"
    },
    {
      title: "Fitness Equipment",
      description: "Professional training gear",
      itemCount: "80+ items",
      image: "https://images.pexels.com/photos/4162449/pexels-photo-4162449.jpeg?auto=compress&cs=tinysrgb&w=600",
      category: "sportsgear"
    },
    {
      title: "Team Sports Gear",
      description: "Equipment for team sports",
      itemCount: "120+ items",
      image: "https://images.pexels.com/photos/2526878/pexels-photo-2526878.jpeg?auto=compress&cs=tinysrgb&w=600",
      category: "sportsgear"
    },
    {
      title: "Compression Wear",
      description: "Advanced compression technology",
      itemCount: "90+ items",
      image: "https://images.pexels.com/photos/4056723/pexels-photo-4056723.jpeg?auto=compress&cs=tinysrgb&w=600",
      category: "sportswear"
    },
    {
      title: "Recovery & Wellness",
      description: "Tools for optimal recovery",
      itemCount: "60+ items",
      image: "https://images.pexels.com/photos/3780681/pexels-photo-3780681.jpeg?auto=compress&cs=tinysrgb&w=600",
      category: "sportsgear"
    }
  ];

  return (
    <section className="py-20" style={{ backgroundColor: 'rgb(26, 26, 26)' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4" style={{ color: '#f8fafc' }}>Shop by Category</h2>
          <p className="text-xl max-w-3xl mx-auto" style={{ color: '#94a3b8' }}>
            Explore our comprehensive collection of premium sportswear and professional-grade equipment 
            designed for athletes at every level.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {(categories || staticCategories).map((category, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -5, scale: 1.02 }}
              className="group relative border rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 cursor-pointer"
              style={{
                backgroundColor: 'rgb(42, 42, 42)',
                borderColor: '#4a4a4a'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = '#ef4444';
                e.currentTarget.style.boxShadow = '0 25px 50px -12px rgba(239, 68, 68, 0.2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = '#4a4a4a';
                e.currentTarget.style.boxShadow = '';
              }}
            >
              <div className="relative h-48 overflow-hidden">
                <motion.img
                  src={category.image}
                  alt={category.title}
                  className="w-full h-full object-cover"
                  whileHover={{ scale: 1.15 }}
                  transition={{ duration: 0.6 }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent group-hover:from-black/80 transition-all duration-300"></div>

                <motion.div
                  className="absolute top-3 left-3"
                  initial={{ scale: 0, rotate: -10 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2 + index * 0.1 }}
                >
                  <span className="px-2 py-1 rounded-full text-xs font-medium shadow-lg"
                    style={{
                      backgroundColor: category.category === 'sportswear' ? '#ef4444' : '#f8fafc',
                      color: category.category === 'sportswear' ? '#ffffff' : '#1a1a1a'
                    }}>
                    {category.category === 'sportswear' ? 'Sportswear' : 'Sports Gear'}
                  </span>
                </motion.div>

                {/* Hover overlay with explore button */}
                <motion.div
                  className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  initial={{ scale: 0.8 }}
                  whileHover={{ scale: 1 }}
                >
                  <motion.button
                    className="bg-white/90 backdrop-blur-sm text-black px-6 py-2 rounded-full font-medium hover:bg-white transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Explore Category
                  </motion.button>
                </motion.div>
              </div>
              
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <motion.h3
                    className="text-lg font-bold transition-colors"
                    style={{ color: '#f8fafc' }}
                    whileHover={{ scale: 1.02 }}
                    onMouseEnter={(e) => e.target.style.color = '#ef4444'}
                    onMouseLeave={(e) => e.target.style.color = '#f8fafc'}
                  >
                    {category.title}
                  </motion.h3>
                  <motion.div
                    whileHover={{ x: 4 }}
                    transition={{ type: "spring", stiffness: 400 }}
                  >
                    <ArrowRight className="h-4 w-4 transition-colors duration-300" style={{ color: '#94a3b8' }} />
                  </motion.div>
                </div>

                <p className="text-sm mb-3 line-clamp-2" style={{ color: '#94a3b8' }}>{category.description}</p>

                <div className="flex items-center justify-between">
                  <motion.span
                    className="text-sm font-medium"
                    style={{ color: '#ef4444' }}
                    whileHover={{ scale: 1.05 }}
                  >
                    {category.itemCount}
                  </motion.span>
                  <motion.button
                    className="text-sm transition-colors font-medium flex items-center gap-1"
                    style={{ color: '#f8fafc' }}
                    whileHover={{ x: 2 }}
                    onMouseEnter={(e) => e.target.style.color = '#ef4444'}
                    onMouseLeave={(e) => e.target.style.color = '#f8fafc'}
                  >
                    Explore
                    <ArrowRight className="h-3 w-3" />
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="text-center mt-12">
          <button className="bg-red-500 hover:bg-red-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
            View All Categories
          </button>
        </div>
      </div>
    </section>
  );
};

export default CategoriesSection;