import React from 'react';
import { Users, Star, Globe, Package, Headphones, Award } from 'lucide-react';

const SuccessSection = () => {
  const achievements = [
    {
      icon: Users,
      number: "500,000+",
      label: "Satisfied Athletes",
      description: "Athletes trust our products"
    },
    {
      icon: Star,
      number: "98%",
      label: "Customer Satisfaction",
      description: "Exceptional quality rating"
    },
    {
      icon: Globe,
      number: "150+",
      label: "Countries Served",
      description: "Global reach and delivery"
    },
    {
      icon: Package,
      number: "2M+",
      label: "Products Delivered",
      description: "Successful deliveries worldwide"
    },
    {
      icon: Headphones,
      number: "24/7",
      label: "Customer Support",
      description: "Always here to help"
    },
    {
      icon: Award,
      number: "15 Years",
      label: "of Excellence",
      description: "Industry experience"
    }
  ];

  return (
    <section className="py-20" style={{ backgroundColor: 'rgb(26, 26, 26)' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4" style={{ color: '#f8fafc' }}>
            Trusted by Champions Worldwide
          </h2>
          <p className="text-xl max-w-3xl mx-auto" style={{ color: '#94a3b8' }}>
            Our commitment to excellence has earned the trust of athletes across the globe, 
            delivering premium quality and unmatched performance in every product.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {achievements.map((achievement, index) => {
            const IconComponent = achievement.icon;
            return (
              <div
                key={index}
                className="border rounded-xl p-8 text-center transition-colors group"
                style={{
                  backgroundColor: 'rgb(42, 42, 42)',
                  borderColor: '#4a4a4a'
                }}
                onMouseEnter={(e) => e.target.style.borderColor = '#ef4444'}
                onMouseLeave={(e) => e.target.style.borderColor = '#4a4a4a'}
              >
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-6 group-hover:scale-110 transition-transform" style={{ backgroundColor: '#ef4444' }}>
                  <IconComponent className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-3xl font-bold mb-2" style={{ color: '#f8fafc' }}>{achievement.number}</h3>
                <h4 className="text-lg font-semibold mb-2" style={{ color: '#ef4444' }}>{achievement.label}</h4>
                <p style={{ color: '#94a3b8' }}>{achievement.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default SuccessSection;